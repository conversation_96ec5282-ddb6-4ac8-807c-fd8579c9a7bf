<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="VcsDirectoryMappings">
    <mapping directory="$PROJECT_DIR$/fast_store_rust" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/logic_engine_rust" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/lua_device_rust" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/request_rust" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/resource_rust" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/rust_uplus" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/security_rust" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/storage_rust" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/task_manager_rust" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/updevice_rust" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/uplus_main_rust" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/usdk_rust" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/userdomain_rust" vcs="Git" />
  </component>
</project>