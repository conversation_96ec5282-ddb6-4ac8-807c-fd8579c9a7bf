<?xml version="1.0" encoding="UTF-8"?>
<module type="CPP_MODULE" version="4">
  <component name="NewModuleRootManager">
    <content url="file://$MODULE_DIR$">
      <sourceFolder url="file://$MODULE_DIR$/request_rust/request_rust/src" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/resource_rust/rust_resource/src" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/resource_rust/rust_resource/tests" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/security_rust/rust_security/src" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/storage_rust/rust_storage/src" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/storage_rust/rust_storage/tests" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/task_manager_rust/task_manager_rust/src" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/task_manager_rust/task_manager_rust/tests" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/uplus_rust/rust_uplus/src" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/userdomain_rust/rust_userdomain/src" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/userdomain_rust/rust_userdomain/tests" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/logic_engine_rust/rust_logicEngine/src" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/logic_engine_rust/rust_logicEngine/tests" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/updevice_rust/rust_updevice/src" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/updevice_rust/rust_updevice/tests" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/usdk_rust/rust_usdk/src" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/main_rust/rust_main/src" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/main_rust/rust_main/tests" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/rust_uplus/rust_uplus/src" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/uplus_main_rust/rust_main_uplus/src" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/uplus_main_rust/rust_main_uplus/tests" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/fast_store_rust/rust_store_rust/src" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/lua_device_rust/rust_lua_device/src" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/lua_device_rust/rust_lua_device/tests" isTestSource="true" />
      <excludeFolder url="file://$MODULE_DIR$/target" />
      <excludeFolder url="file://$MODULE_DIR$/uplus_rust/rust_uplus/target" />
      <excludeFolder url="file://$MODULE_DIR$/rust_uplus/rust_uplus/target" />
    </content>
    <orderEntry type="inheritedJdk" />
    <orderEntry type="sourceFolder" forTests="false" />
  </component>
</module>