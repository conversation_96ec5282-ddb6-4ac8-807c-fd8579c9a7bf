use std::collections::HashMap;
use std::hash::Hash;

use flatbuffers::{FlatBufferBuilder, ForwardsUOffset, UnionWIPOffset, Vector, WIPOffset};
use log::{debug, error, info, warn};

use task_manager::platform::function::PlatformConsumer;

use crate::features::lua_device_generated::com::haier::uhome::uplus::rust::lua_device::fbs::*;
use crate::models::card_model::{
    Attribute, CardModel, DeviceState, OptionItem, Service, ValueRange,
};
use crate::models::command_result::{DeviceFeedback, ErrorInfo};
use crate::models::error::{<PERSON>rrorCode, ScriptInterpreterError};
use crate::modules::up_script_engine::{UpScriptEngine, UpScriptEngineEvent};

static EMPTY: String = String::new();
const ACTION: &str = "action";
const DEFAULT_SIZE: usize = 1024;
const SUCCESS_CODE: &str = "000000";
const PARAMS_ERROR: &str = "900003";

macro_rules! require_params {
    ($params:expr, $($param:expr),+) => {
        for &param in &[$($param),+] {
            if !$params.contains_key(param) {
                warn!("lua_device: required parameter '{}' is missing", param);
                return invalid_arg_result(&format!("{} is required", param));
            }
        }
    };
}

pub fn lib_lua_device_cross_platform(params: HashMap<String, String>) -> Vec<u8> {
    let action = params.get(ACTION).unwrap_or(&EMPTY).as_str();
    info!("lua_device: executing action: {}", action);
    match action {
        "init" => init(params),
        "get_device_card_by_device_id" => get_device_card_by_device_id(params),
        "get_devices_card_by_family_id" => get_devices_card_by_family_id(params),
        "unsubscribe_device_list_change" => unsubscribe_device_list_change(params),
        "unsubscribe_device_card" => unsubscribe_device_card(params),
        _ => {
            warn!("lua_device: unsupported action: {}", action);
            invalid_arg_result("unsupported action")
        }
    }
}

pub async fn lib_lua_device_cross_platform_async(params: HashMap<String, String>) -> Vec<u8> {
    let action = params.get(ACTION).unwrap_or(&EMPTY).as_str();
    info!("lua_device: executing async action: {}", action);
    match action {
        "operate_device_command" => operate_device_command(params).await,
        "update_scripts" => update_scripts(params).await,
        "update_device_list" => update_device_list(params).await,
        "update_device_attributes" => update_device_attributes(params).await,
        "set_device_command_executor" => set_device_command_executor(params).await,
        _ => {
            warn!("Unsupported async action: {}", action);
            invalid_arg_result("unsupported action")
        }
    }
}

pub fn lib_lua_device_cross_platform_consumer_data(
    params: HashMap<String, String>,
    consumer: impl PlatformConsumer + 'static,
) -> Vec<u8> {
    let action = params.get(ACTION).unwrap_or(&EMPTY).as_str();
    info!("lua_device: executing action: {}", action);
    match action {
        "subscribe_device_list_change" => subscribe_device_list_change(params, consumer),
        "subscribe_device_card" => subscribe_device_card(params, consumer),
        _ => invalid_arg_result("unsupported action"),
    }
}

fn init(params: HashMap<String, String>) -> Vec<u8> {
    #[cfg(feature = "android")]
    {
        rust_storage::features::flat
        rust_storage::features::android::init_logger();
    }
    #[cfg(any(feature = "android", feature = "ios"))]
    {
        use request_rust::setting::request_setting::RequestConfigManager;
        use rust_storage::api::storage_manager::StorageManager;
        use rust_storage::database::db_executor::DbExecutor;

        require_params!(
            params,
            "app_id",
            "app_key",
            "app_version",
            "client_id",
            "network_env",
            "path"
        );
        let database_path = params.get("path").unwrap_or(&EMPTY);
        match DbExecutor::new(database_path) {
            Ok(database) => StorageManager::get_instance().set_db(Box::new(database)),
            Err(e) => warn!("error creating storage manager: {:?}", e),
        }
        let network_env = params
            .get("network_env")
            .unwrap()
            .parse()
            .unwrap_or(2)
            .into();
        info!("init network_env {:?}", network_env);
        RequestConfigManager::get_instance().write_settings(|setting| {
            setting.set_app_id(params.get("app_id").unwrap().to_string());
            setting.set_app_key(params.get("app_key").unwrap().to_string());
            setting.set_app_version(params.get("app_version").unwrap().to_string());
            setting.set_client_id(params.get("client_id").unwrap().to_string());
            setting.set_root_certificates(vec![]);
            setting.set_network_env(network_env);
        });
    }

    let engine = UpScriptEngine::get_instance();
    engine.init();
    success_result()
}

fn get_device_card_by_device_id(params: HashMap<String, String>) -> Vec<u8> {
    require_params!(params, "device_id");
    let device_id = params.get("device_id").unwrap().to_string();
    let mut builder = FlatBufferBuilder::with_capacity(DEFAULT_SIZE);

    let engine = UpScriptEngine::get_instance();
    let card_model = engine.get_device_card_by(&device_id);

    if let Some(temp_card_model) = card_model {
        let fbs_card_model = create_fbs_card_model(&mut builder, &temp_card_model);
        data_result(
            &mut builder,
            fbs_card_model.as_union_value(),
            LuaDeviceContainer::FBSCardModel,
        )
    } else {
        success_result()
    }
}

fn get_devices_card_by_family_id(params: HashMap<String, String>) -> Vec<u8> {
    require_params!(params, "family_id");
    let family_id = params.get("family_id").unwrap().to_string();
    let floor_id = params.get("floor_id").map(|s| s.as_str());

    let mut builder = FlatBufferBuilder::with_capacity(DEFAULT_SIZE);

    let engine = UpScriptEngine::get_instance();
    let card_model_list = engine.get_devices_card_by(&family_id, floor_id);
    let fbs_card_models = card_model_list
        .iter()
        .map(|card_model| create_fbs_card_model(&mut builder, card_model))
        .collect::<Vec<_>>();
    let fbs_card_model_list = builder.create_vector(&fbs_card_models);
    data_result(
        &mut builder,
        fbs_card_model_list.as_union_value(),
        LuaDeviceContainer::FBSCardModel,
    )
}

fn subscribe_device_list_change(
    params: HashMap<String, String>,
    consumer: impl PlatformConsumer + 'static,
) -> Vec<u8> {
    require_params!(params, "family_id");
    let family_id = params.get("family_id").unwrap().to_string();
    let observer = create_device_list_observer(consumer);
    let engine = UpScriptEngine::get_mut_instance();
    let id = engine.subscribe_device_list_change(&family_id, observer);
    string_result(id)
}

fn unsubscribe_device_list_change(params: HashMap<String, String>) -> Vec<u8> {
    require_params!(params, "subscribe_id");
    let subscribe_id = params.get("subscribe_id").unwrap();
    let engine = UpScriptEngine::get_mut_instance();
    engine.unsubscribe_device_list_change(subscribe_id);

    success_result()
}

fn subscribe_device_card(
    params: HashMap<String, String>,
    consumer: impl PlatformConsumer + 'static,
) -> Vec<u8> {
    require_params!(params, "family_id");
    let family_id = params.get("family_id").unwrap().to_string();
    let floor_id = params.get("floor_id").map(|s| s.as_str());

    let observer = create_card_model_observer(consumer);
    let engine = UpScriptEngine::get_mut_instance();
    let id = engine.subscribe_device_card_by(&family_id, floor_id, observer);
    string_result(id)
}

fn unsubscribe_device_card(params: HashMap<String, String>) -> Vec<u8> {
    require_params!(params, "subscribe_id");
    let subscribe_id = params.get("subscribe_id").unwrap();
    let engine = UpScriptEngine::get_mut_instance();
    engine.unsubscribe_device_card_by(subscribe_id);

    success_result()
}

async fn operate_device_command(params: HashMap<String, String>) -> Vec<u8> {
    require_params!(params, "device_id", "services_id", "attributes");

    let device_id = params.get("device_id").unwrap().to_string();
    let services_id = params.get("services_id").unwrap().to_string();
    let attributes_str = params.get("attributes").unwrap();
    let attributes: HashMap<String, String> = match serde_json::from_str(attributes_str) {
        Ok(map) => map,
        Err(e) => {
            warn!("Failed to parse attributes: {}", e);
            HashMap::new() // TODO：转换失败，是否要继续执行命令还是直接返回？
        }
    };

    let engine = UpScriptEngine::get_instance();
    let result = engine
        .operate_device_command_by(&device_id, &services_id, attributes)
        .await;
    let mut builder = FlatBufferBuilder::with_capacity(DEFAULT_SIZE);
    match result {
        Ok(device_feedback) => {
            let fbs_device_feedback = create_fbs_device_feedback(&mut builder, &device_feedback);
            data_result(
                &mut builder,
                fbs_device_feedback.as_union_value(),
                LuaDeviceContainer::FBSDeviceFeedback,
            )
        }
        Err(err) => {
            let cross_err: CrossPlatformError = err.into();
            failure_result(&cross_err.message, &cross_err.code.to_string())
        }
    }
}

async fn update_scripts(params: HashMap<String, String>) -> Vec<u8> {
    require_params!(params, "scripts");
    let scripts_str = params.get("scripts").unwrap();

    let engine = UpScriptEngine::get_instance();
    // engine_guard.update_scripts(serde_json::from_str(scripts_str).unwrap());
    success_result()
}

async fn update_device_list(params: HashMap<String, String>) -> Vec<u8> {
    require_params!(params, "devices");
    let devices_str = params.get("devices").unwrap();

    let engine = UpScriptEngine::get_instance();
    // engine_guard.update_device_list(serde_json::from_str(devices_str).unwrap());
    success_result()
}

async fn update_device_attributes(params: HashMap<String, String>) -> Vec<u8> {
    require_params!(params, "devices");
    let devices_str = params.get("devices").unwrap();

    let engine = UpScriptEngine::get_instance();
    // engine_guard.update_device_attributes(serde_json::from_str(devices_str).unwrap());
    success_result()
}

async fn set_device_command_executor(params: HashMap<String, String>) -> Vec<u8> {
    todo!()
}

// Create observer

fn create_card_model_observer(
    consumer: impl PlatformConsumer + 'static,
) -> impl Fn(UpScriptEngineEvent) + Send + Sync + 'static {
    move |event| {
        let mut builder = FlatBufferBuilder::with_capacity(DEFAULT_SIZE);
        let (code, container_type, container) = match event {
            UpScriptEngineEvent::CardChangedEvent(card_model_list) => {
                let fbs_card_models = card_model_list
                    .iter()
                    .map(|card_model| create_fbs_card_model(&mut builder, card_model))
                    .collect::<Vec<_>>();
                let fbs_card_model_list = builder.create_vector(&fbs_card_models);
                (
                    1,
                    LuaDeviceContainer::FBSCardModelList,
                    Some(fbs_card_model_list.as_union_value()),
                )
            }
            _ => {
                return;
            }
        };
        let lua_device_message = FBSLuaDeviceMessage::create(
            &mut builder,
            &FBSLuaDeviceMessageArgs {
                code,
                container_type,
                container,
            },
        );
        builder.finish(lua_device_message, None);
        let data = builder.finished_data().to_vec();
        consumer.accept(data);
    }
}

fn create_device_list_observer(
    consumer: impl PlatformConsumer + 'static,
) -> impl Fn(UpScriptEngineEvent) + Send + Sync + 'static {
    move |event| {
        let mut builder = FlatBufferBuilder::with_capacity(DEFAULT_SIZE);
        let (code, container_type, container) = match event {
            UpScriptEngineEvent::DeviceListChangedEvent(family_id) => {
                let fbs_family_id = builder.create_string(&family_id);
                (
                    2,
                    LuaDeviceContainer::StrWrapper,
                    Some(fbs_family_id.as_union_value()),
                )
            }
            _ => {
                return;
            }
        };
        let lua_device_message = FBSLuaDeviceMessage::create(
            &mut builder,
            &FBSLuaDeviceMessageArgs {
                code,
                container_type,
                container,
            },
        );
        builder.finish(lua_device_message, None);
        let data = builder.finished_data().to_vec();
        consumer.accept(data);
    }
}

// Create model

fn create_fbs_card_model<'a>(
    builder: &mut FlatBufferBuilder<'a>,
    card_model: &CardModel,
) -> WIPOffset<FBSCardModel<'a>> {
    let device_id = builder.create_string(&card_model.device_id);
    let device_type = builder.create_string(&card_model.device_type);
    let device_name = builder.create_string(&card_model.device_name);
    let device_image = builder.create_string(&card_model.device_image);
    let floor_id = builder.create_string(&card_model.floor_id);
    let floor_name = builder.create_string(&card_model.floor_name);
    let room_id = builder.create_string(&card_model.room_id);
    let room_name = builder.create_string(&card_model.room_name);
    let app_type_code = builder.create_string(&card_model.app_type_code);
    let app_type_name = builder.create_string(&card_model.app_type_name);
    let parent_id = builder.create_string(&card_model.parent_id);
    let online_state = builder.create_string(&card_model.online_state);
    let device_state = if let Some(some_device_state) = &card_model.device_state {
        Some(create_fbs_device_state(builder, some_device_state))
    } else {
        None
    };

    let services = if let Some(some_services) = &card_model.services {
        let fbs_services = some_services
            .iter()
            .map(|service| create_fbs_service(builder, service))
            .collect::<Vec<_>>();
        Some(builder.create_vector(&fbs_services))
    } else {
        None
    };

    let device_tags = &card_model
        .device_tags
        .iter()
        .map(|tag| builder.create_string(tag))
        .collect::<Vec<_>>();
    let fbs_device_tags = builder.create_vector(&device_tags);

    FBSCardModel::create(
        builder,
        &FBSCardModelArgs {
            device_id: Some(device_id),
            device_type: Some(device_type),
            floor_id: Some(floor_id),
            floor_name: Some(floor_name),
            device_name: Some(device_name),
            device_image: Some(device_image),
            room_id: Some(room_id),
            room_name: Some(room_name),
            app_type_code: Some(app_type_code),
            app_type_name: Some(app_type_name),
            parent_id: Some(parent_id),
            online_state: Some(online_state),
            device_state,
            services,
            device_tags: Some(fbs_device_tags),
        },
    )
}

fn create_fbs_device_state<'a>(
    builder: &mut FlatBufferBuilder<'a>,
    device_state: &DeviceState,
) -> WIPOffset<FBSDeviceState<'a>> {
    let value = builder.create_string(&device_state.value);
    let label = builder.create_string(&device_state.label);
    FBSDeviceState::create(
        builder,
        &FBSDeviceStateArgs {
            value: Some(value),
            label: Some(label),
        },
    )
}

fn create_fbs_service<'a>(
    builder: &mut FlatBufferBuilder<'a>,
    service: &Service,
) -> WIPOffset<FBSService<'a>> {
    let name = builder.create_string(&service.name);
    let label = builder.create_string(&service.label);
    let feature = service
        .feature
        .iter()
        .map(|it| builder.create_string(it))
        .collect::<Vec<_>>();
    let fbs_feature = builder.create_vector(&feature);
    let attributes = service
        .attributes
        .iter()
        .map(|it| create_fbs_attribute(builder, it))
        .collect::<Vec<_>>();
    let fbs_attributes = builder.create_vector(&attributes);
    let fbs_service = FBSService::create(
        builder,
        &FBSServiceArgs {
            id: service.id,
            name: Some(name),
            label: Some(label),
            feature: Some(fbs_feature),
            attributes: Some(fbs_attributes),
        },
    );
    fbs_service
}

fn create_fbs_attribute<'a>(
    builder: &mut FlatBufferBuilder<'a>,
    attribute: &Attribute,
) -> WIPOffset<FBSAttribute<'a>> {
    let name = builder.create_string(&attribute.name);
    let label = builder.create_string(&attribute.label);
    let value = builder.create_string(&attribute.value);
    let value_type = builder.create_string(&attribute.value_type);

    let fbs_value_range = match &attribute.value_range {
        Some(value_range) => Some(create_fbs_value_range(builder, value_range)),
        None => None,
    };

    FBSAttribute::create(
        builder,
        &FBSAttributeArgs {
            name: Some(name),
            label: Some(label),
            value: Some(value),
            value_type: Some(value_type),
            value_range: fbs_value_range,
            writable: attribute.writable,
        },
    )
}

fn create_fbs_value_range<'a>(
    builder: &mut FlatBufferBuilder<'a>,
    value_range: &ValueRange,
) -> WIPOffset<FBSValueRange<'a>> {
    let mut fbs_range_type = None;
    let mut fbs_min_value = None;
    let mut fbs_max_value = None;
    let mut fbs_step = None;
    let mut fbs_name_space = None;
    let mut fbs_option_list = None;

    match value_range {
        ValueRange::Numeric {
            range_type,
            min_value,
            max_value,
            step,
        } => {
            fbs_range_type = Some(builder.create_string(range_type));
            fbs_min_value = Some(builder.create_string(min_value));
            fbs_max_value = Some(builder.create_string(max_value));
            fbs_step = Some(builder.create_string(step));
        }
        ValueRange::List {
            range_type,
            namespace,
            option_list,
        } => {
            fbs_range_type = Some(builder.create_string(range_type));
            fbs_name_space = Some(builder.create_string(namespace));
            let temp_option_list = Some(
                option_list
                    .iter()
                    .map(|it| create_fbs_option_item(builder, it))
                    .collect::<Vec<_>>(),
            );

            fbs_option_list = match temp_option_list {
                Some(option_list) => Some(builder.create_vector(&option_list)),
                None => None,
            };
        }
    };

    FBSValueRange::create(
        builder,
        &FBSValueRangeArgs {
            range_type: fbs_range_type,
            min_value: fbs_min_value,
            max_value: fbs_max_value,
            step: fbs_step,
            name_space: fbs_name_space,
            option_list: fbs_option_list,
        },
    )
}

fn create_fbs_option_item<'a>(
    builder: &mut FlatBufferBuilder<'a>,
    option_item: &OptionItem,
) -> WIPOffset<FBSOptionItem<'a>> {
    let value = builder.create_string(&option_item.value);
    let label = builder.create_string(&option_item.label);

    FBSOptionItem::create(
        builder,
        &FBSOptionItemArgs {
            value: Some(value),
            label: Some(label),
        },
    )
}

fn create_fbs_device_feedback<'a>(
    builder: &mut FlatBufferBuilder<'a>,
    device_feedback: &DeviceFeedback,
) -> WIPOffset<FBSDeviceFeedback<'a>> {
    let device_id = builder.create_string(&device_feedback.device_id);
    let fbs_error_info = create_fbs_error_info(builder, &device_feedback.error_info);

    FBSDeviceFeedback::create(
        builder,
        &FBSDeviceFeedbackArgs {
            device_id: Some(device_id),
            req_sn: device_feedback.req_sn,
            error_info: Some(fbs_error_info),
        },
    )
}

fn create_fbs_error_info<'a>(
    builder: &mut FlatBufferBuilder<'a>,
    error_info: &ErrorInfo,
) -> WIPOffset<FBSErrorInfo<'a>> {
    let desc = builder.create_string(&error_info.desc);
    let pari_list = create_fbs_string_entry_map(builder, &error_info.pair_list);
    let fbs_pair_list = builder.create_vector(&pari_list);

    FBSErrorInfo::create(
        builder,
        &FBSErrorInfoArgs {
            code: error_info.code,
            desc: Some(desc),
            pair_list: Some(fbs_pair_list),
        },
    )
}

fn create_fbs_string_entry_map<'a>(
    builder: &mut FlatBufferBuilder<'a>,
    hash_map: &HashMap<String, String>,
) -> Vec<WIPOffset<StringEntryWrapper<'a>>> {
    hash_map
        .iter()
        .map(|(key, value)| {
            let key = builder.create_string(key);
            let value = builder.create_string(value);
            StringEntryWrapper::create(
                builder,
                &StringEntryWrapperArgs {
                    key: None,
                    value: None,
                },
            )
        })
        .collect::<Vec<_>>()
}

// Result

fn invalid_arg_result(error_message: &str) -> Vec<u8> {
    failure_result(error_message, PARAMS_ERROR)
}

fn failure_result(error_message: &str, error_code: &str) -> Vec<u8> {
    error!(
        "lua_device ffi errormessage:{},error_code:{}",
        error_message, error_code
    );
    let mut builder = FlatBufferBuilder::with_capacity(DEFAULT_SIZE);
    let error_message = builder.create_string(error_message);
    let error_code = builder.create_string(error_code);
    let none_wrapper = NoneWrapper::create(&mut builder, &NoneWrapperArgs {});
    let lua_device_result = LuaDeviceFlat::create(
        &mut builder,
        &LuaDeviceFlatArgs {
            container_type: LuaDeviceContainer::NoneWrapper,
            container: Some(none_wrapper.as_union_value()),
            code: Some(error_code),
            error: Some(error_message),
        },
    );
    builder.finish(lua_device_result, None);
    builder.finished_data().to_vec()
}

fn bool_result(value: bool) -> Vec<u8> {
    let mut builder = FlatBufferBuilder::with_capacity(DEFAULT_SIZE);
    let bool_result = BoolWrapper::create(&mut builder, &BoolWrapperArgs { value });
    data_result(
        &mut builder,
        bool_result.as_union_value(),
        LuaDeviceContainer::BoolWrapper,
    )
}

fn i32_result(value: i32) -> Vec<u8> {
    let mut builder = FlatBufferBuilder::with_capacity(DEFAULT_SIZE);
    let state = Int32Wrapper::create(&mut builder, &Int32WrapperArgs { value });
    data_result(
        &mut builder,
        state.as_union_value(),
        LuaDeviceContainer::Int32Wrapper,
    )
}

fn string_result(result: String) -> Vec<u8> {
    let mut builder = FlatBufferBuilder::with_capacity(DEFAULT_SIZE);
    let value = builder.create_string(&result);
    let string_result = StrWrapper::create(&mut builder, &StrWrapperArgs { value: Some(value) });
    data_result(
        &mut builder,
        string_result.as_union_value(),
        LuaDeviceContainer::StrWrapper,
    )
}

fn data_result(
    builder: &mut FlatBufferBuilder,
    data: WIPOffset<UnionWIPOffset>,
    container_type: LuaDeviceContainer,
) -> Vec<u8> {
    let code = builder.create_string(SUCCESS_CODE);
    let lua_device_result = LuaDeviceFlat::create(
        builder,
        &LuaDeviceFlatArgs {
            container_type,
            container: Some(data),
            code: Some(code),
            error: None,
        },
    );
    builder.finish(lua_device_result, None);
    builder.finished_data().to_vec()
}

fn success_result() -> Vec<u8> {
    debug!("Operation successful");
    let mut builder = FlatBufferBuilder::with_capacity(DEFAULT_SIZE);
    let none_wrapper = NoneWrapper::create(&mut builder, &NoneWrapperArgs {});
    let code = builder.create_string(SUCCESS_CODE);
    let lua_device_result = LuaDeviceFlat::create(
        &mut builder,
        &LuaDeviceFlatArgs {
            container_type: LuaDeviceContainer::NoneWrapper,
            container: Some(none_wrapper.as_union_value()),
            code: Some(code),
            error: None,
        },
    );
    builder.finish(lua_device_result, None);
    builder.finished_data().to_vec()
}

// Error

#[derive(Debug, Clone)]
pub struct CrossPlatformError {
    pub code: ErrorCode,
    pub message: String,
}

impl CrossPlatformError {
    // 构造函数
    pub fn new(code: ErrorCode, message: String) -> Self {
        Self { code, message }
    }
}

// 实现错误转换
impl From<ScriptInterpreterError> for CrossPlatformError {
    fn from(err: ScriptInterpreterError) -> Self {
        match err {
            ScriptInterpreterError::General { code, message } => {
                CrossPlatformError::new(code, message)
            }
            ScriptInterpreterError::ScriptExecution { code, message } => {
                CrossPlatformError::new(code, message)
            }
            ScriptInterpreterError::ServiceExecuteError { code, message } => {
                CrossPlatformError::new(code, message)
            }
            ScriptInterpreterError::DeviceError { code, message } => {
                CrossPlatformError::new(code, message)
            }
        }
    }
}
