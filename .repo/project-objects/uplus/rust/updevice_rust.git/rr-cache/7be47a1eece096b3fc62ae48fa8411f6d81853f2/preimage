use rust_userdomain::models::device_info::{DeviceInfo, ShareDeviceCardInfo};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

use crate::models::device_base_info::UpDeviceBaseInfo;
use crate::models::device_basic::UpDeviceBasic;
use crate::models::device_permission::UpDevicePermission;
use crate::models::device_product::{
    UpDeviceProduct, UpDeviceProductBuilder, UpDeviceShareCardInfo,
};
use crate::models::device_relation::UpDeviceRelation;

const WIFI_DEVICE_TOOLKIT_PROTOCOL: &str = "haier-usdk";

#[derive(Default, Clone, Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase", default)]
pub struct UpDeviceInfo {
    device_base_info: UpDeviceBaseInfo,
    device_basic: UpDeviceBasic,
    device_permission: UpDevicePermission,
    device_product: UpDeviceProduct,
    device_relation: UpDeviceRelation,
}

impl UpDeviceInfo {
    #[allow(clippy::too_many_arguments)]
    pub fn new(
        protocol: String,
        device_id: String,
        type_id: Option<String>,
        type_code: Option<String>,
        model: String,
        product_code: String,
        parent_id: Option<String>,
        sub_device_id: String,
        device_basic: UpDeviceBasic,
        device_permission: UpDevicePermission,
        device_product: UpDeviceProduct,
        device_relation: UpDeviceRelation,
    ) -> Self {
        UpDeviceInfo {
            device_base_info: UpDeviceBaseInfo::new(
                protocol,
                device_id,
                type_id,
                type_code,
                model,
                product_code,
                parent_id,
                sub_device_id,
            ),
            device_basic,
            device_permission,
            device_product,
            device_relation,
        }
    }

    pub fn init_with_base_info(base_info: UpDeviceBaseInfo) -> Self {
        UpDeviceInfo {
            device_base_info: base_info,
            device_basic: UpDeviceBasic::empty(),
            device_permission: UpDevicePermission::empty(),
            device_product: UpDeviceProduct::empty(),
            device_relation: UpDeviceRelation::empty(),
        }
    }

    pub fn empty() -> Self {
        UpDeviceInfo {
            device_base_info: UpDeviceBaseInfo::empty(),
            device_basic: UpDeviceBasic::empty(),
            device_permission: UpDevicePermission::empty(),
            device_product: UpDeviceProduct::empty(),
            device_relation: UpDeviceRelation::empty(),
        }
    }

    pub fn device_id(&self) -> String {
        self.get_base_info().device_id().clone()
    }
    pub fn protocol(&self) -> String {
        self.get_base_info().protocol().clone()
    }
    pub fn type_id(&self) -> Option<String> {
        self.get_base_info().type_id().clone()
    }
    pub fn type_code(&self) -> Option<String> {
        self.get_base_info().type_code().clone()
    }
    pub fn model(&self) -> String {
        self.get_base_info().model().clone()
    }
    pub fn product_code(&self) -> String {
        self.get_base_info().product_code().clone()
    }
    pub fn parent_id(&self) -> Option<String> {
        self.get_base_info().parent_id().clone()
    }
    pub fn sub_device_id(&self) -> String {
        self.get_base_info().sub_device_id().clone()
    }

    pub fn get_base_info(&self) -> &UpDeviceBaseInfo {
        &self.device_base_info
    }
    pub fn get_basic(&self) -> &UpDeviceBasic {
        &self.device_basic
    }
    pub fn get_permission(&self) -> &UpDevicePermission {
        &self.device_permission
    }
    pub fn get_product(&self) -> &UpDeviceProduct {
        &self.device_product
    }
    pub fn get_relation(&self) -> &UpDeviceRelation {
        &self.device_relation
    }

    pub fn update_base_info(&mut self, base_info: UpDeviceBaseInfo) {
        self.device_base_info = base_info;
    }

    pub fn update_device_info(&mut self, device_info: UpDeviceInfo) {
        self.device_base_info = device_info.device_base_info;
        self.device_basic = device_info.device_basic;
        self.device_permission = device_info.device_permission;
        self.device_product = device_info.device_product;
        self.device_relation = device_info.device_relation;
    }

    pub fn get_device_card_is_aggregate_device(&self) -> bool {
        !self.device_basic.device_aggregate_type().is_empty()
    }

    pub fn get_card_info(&self, family_id: &String) -> UpDeviceShareCardInfo {
        let card_info_map = self.device_product.card_info_map();
        let info = card_info_map.get(family_id);
        match info {
            Some(info) => info.clone(),
            None => UpDeviceShareCardInfo {
                family_id: family_id.clone(),
                card_sort: self.device_product.card_sort(),
                card_status: self.device_product.card_status(),
            },
        }
    }
}

impl From<DeviceInfo> for UpDeviceInfo {
    fn from(userdomain_device_info: DeviceInfo) -> Self {
        let sub_device_id = userdomain_device_info
            .sub_device_ids
            .first()
            .unwrap_or(&"".to_string())
            .clone();

        let device_basic = UpDeviceBasic::new(
            userdomain_device_info.device_name,
            userdomain_device_info.apptype_name,
            userdomain_device_info.apptype_code.clone(),
            userdomain_device_info.room_name,
            userdomain_device_info.room_id,
            userdomain_device_info.is_online,
            userdomain_device_info.sub_device_ids,
            userdomain_device_info.device_role,
            userdomain_device_info.device_role_type,
            userdomain_device_info.device_net_type,
            userdomain_device_info.device_group_id,
            userdomain_device_info.device_group_type,
            userdomain_device_info.bind_time,
            userdomain_device_info.device_aggregate_type,
            userdomain_device_info.attachment_sort_code,
        );

        let device_permission = UpDevicePermission::new(
            userdomain_device_info.permission.auth_type,
            userdomain_device_info.permission.auth.control,
            userdomain_device_info.permission.auth.set,
            userdomain_device_info.permission.auth.view,
        );

        let device_product = UpDeviceProductBuilder::default()
            .bar_code(userdomain_device_info.barcode)
            .brand(userdomain_device_info.brand)
            .category_grouping(userdomain_device_info.category_grouping)
            .two_grouping_name(userdomain_device_info.two_groping_name)
            .device_type(userdomain_device_info.device_type.clone())
            .image_url(userdomain_device_info.image_addr1)
            .bind_type(userdomain_device_info.bind_type)
            .access_type(userdomain_device_info.access_type)
            .communication_mode(userdomain_device_info.communication_mode)
            .config_type(userdomain_device_info.config_type)
            .app_type_icon(userdomain_device_info.apptype_icon)
            .no_keep_alive(userdomain_device_info.no_keep_alive)
            .card_page_icon(String::new())
            .card_page_img(userdomain_device_info.card_page_img)
            .small_card_sort(0)
            .large_card_sort(0)
            .card_sort(userdomain_device_info.card_sort)
            .card_status(userdomain_device_info.card_status)
            .aggregation_parent_id(userdomain_device_info.aggregation_parent_id)
            .support_aggregation_flag(userdomain_device_info.support_aggregation_flag)
            .card_info_map(convert_user_domain_device_card_info(
                userdomain_device_info.share_device_card_info,
            ))
<<<<<<<
=======
            .shared_device_flag(userdomain_device_info.shared_device_flag == 1)
            .support_shared(userdomain_device_info.device_share_support_flag)
>>>>>>>
            .re_bind(userdomain_device_info.rebind)
            .build()
            .unwrap_or(UpDeviceProduct::empty());

        let device_relation = UpDeviceRelation::new(
            userdomain_device_info.owner_id,
            userdomain_device_info.owner_info.mobile,
            userdomain_device_info.family_id,
            userdomain_device_info.owner_info.uc_user_id,
            userdomain_device_info.device_floor_id,
            userdomain_device_info.device_floor_name,
            userdomain_device_info.device_floor_order_id,
        );

        Self::new(
            WIFI_DEVICE_TOOLKIT_PROTOCOL.to_string(),
            userdomain_device_info.device_id,
            userdomain_device_info.wifi_type,
            userdomain_device_info.device_type,
            userdomain_device_info.model,
            userdomain_device_info.prod_no,
            userdomain_device_info.parent_id,
            sub_device_id,
            device_basic,
            device_permission,
            device_product,
            device_relation,
        )
    }
}

fn convert_user_domain_device_card_info(
    device_card_infos: Vec<ShareDeviceCardInfo>,
) -> HashMap<String, UpDeviceShareCardInfo> {
    let mut info_dic: HashMap<String, UpDeviceShareCardInfo> = HashMap::new();
    device_card_infos.into_iter().for_each(|info| {
        if !info.family_id.is_empty() {
            info_dic.insert(
                info.family_id.clone(),
                UpDeviceShareCardInfo {
                    family_id: info.family_id.clone(),
                    card_sort: info.card_sort,
                    card_status: info.card_status,
                },
            );
        }
    });
    info_dic
}
