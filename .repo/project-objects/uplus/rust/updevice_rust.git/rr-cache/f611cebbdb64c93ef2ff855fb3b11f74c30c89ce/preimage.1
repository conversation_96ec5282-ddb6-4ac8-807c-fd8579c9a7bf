use std::any::Any;
use std::collections::HashMap;
use std::sync::Arc;

use super::updevice_generated::com::haier::uhome::uplus::rust::updevice::fbs::{
    FBSDataItem, FBSDataStep, FBSDataTime,
};
use crate::api;
use crate::api::device_injection::UpDeviceInjection;
use crate::api::device_manager::UpDeviceManager;
use crate::api::event::UpDeviceEvent;
use crate::device::extend_api::ExtendApi;
use crate::device::up_device::UpDevice;
use crate::features::ffi_models::{UpDeviceAttributeModel, UpDeviceModel};
use crate::features::flat::updevice_generated::com::haier::uhome::uplus::rust::updevice::fbs::*;
use crate::models::device_config_state::UpDeviceConfigState;
use crate::models::device_connect_state::UpDeviceConnectState;
use crate::models::device_info::UpDeviceInfo;
use crate::models::device_offline_cause::UpDeviceOfflineCause;
use crate::models::device_online_state::UpDeviceOnlineState;
use crate::models::device_sleep_state::UpDeviceSleepState;
use crate::utils::fn_clone::FnClone;
use api::error::Result;
use flatbuffers::{FlatBufferBuilder, ForwardsUOffset, UnionWIPOffset, Vector, WIPOffset};
use log::{debug, error, info, warn};
use logic_engine::device::command::Command;
use logic_engine::device::device_attribute::UpDeviceAttribute;
use logic_engine::device::device_caution::UpDeviceCaution;
use logic_engine::device::device_command::UpDeviceCommand;
use logic_engine::device_config::business_attr::{BusinessAttr, CmdPara, CmdParaAttr};
use logic_engine::device_config::modifier::{Modifier, ModifierAction, ModifierTrigger};
use logic_engine::device_config::value_range::{
    DateValue, ListValue, StepValue, TimeValue, Transform,
};
use logic_engine::engine::attribute::value_range::date_value_range::LEDateValueRange;
use logic_engine::engine::attribute::value_range::list_value_range::LEDataItem;
use logic_engine::engine::attribute::value_range::step_value_range::LETransform;
use logic_engine::engine::attribute::value_range::time_value_range::LETimeValueRange;
use logic_engine::engine::attribute::value_range::LEValueRange;
use logic_engine::engine::attribute::{DataStep, LEAttribute, ValueRange};
use logic_engine::engine::caution::LECaution;
use logic_engine::engine::modifier::action::LEAction;
use logic_engine::engine::modifier::trigger::LETrigger;
use logic_engine::engine::modifier::LEModifier;
use rust_resource::pipelines::pipeline::Pipeline::Single;
use rust_usdk::modules::device::UhsdDeviceAttribute;
use rust_usdk::toolkit_ffi::uhsd_device_group::device_group_model::{
    GroupDeviceAddDelReqInfo, GroupDeviceBaseReqInfo, GroupDeviceReqInfo,
};
use rust_usdk::toolkit_ffi::uhsd_manager::UhsdManager;
use rust_usdk::toolkit_ffi::uhsd_usr_model::{DeviceFeedback, ErrorInfo, UhsdPair};
use rust_usdk::usdk_utils::constants::{
    DEVICE_GROUP_TIMEOUT_ADD_DEL_DEVICES, UHSD_DEFAULT_GROUP_DEVICE_TIMEOUT,
};
use rust_userdomain::api::device::Device;
use rust_userdomain::api::event::UserDomainEvent;
use rust_userdomain::api::user_domain_manager::UserDomainManager;
use serde_json::Value;
use task_manager::common_runtime::get_runtime;
use task_manager::platform::function::PlatformConsumer;
use task_manager::task_manager::get_task_manager;

static EMPTY: String = String::new();
const ACTION: &str = "action";
const SUCCESS: &str = "OK";
const DEFAULT_SIZE: usize = 1024;
const SUCCESS_CODE: &str = "000000";
const PARAMS_ERROR: &str = "900003";
const NO_DEVICE_ERROR: &str = "100001";
const NO_ATTRIBUTE_ERROR: &str = "100002";
const DEVICE_ERROR: &str = "100003";
const GROUPABLE_DEVICES_ERROR: &str = "100004";
const NO_FAMILY_ERROR: &str = "100005";

macro_rules! require_params {
    ($params:expr, $($param:expr),+) => {
        for &param in &[$($param),+] {
            if !$params.contains_key(param) {
                 warn!("updevice: required parameter '{}' is missing", param);
                return invalid_arg_result(&format!("{} is required", param));
            }
        }
    };
}

pub fn lib_updevice_cross_platform(params: HashMap<String, String>) -> Vec<u8> {
    let action = params.get(ACTION).unwrap_or(&EMPTY).as_str();
    info!("updevice: executing action: {}", action);
    match action {
        "init" => init(params),
        "get_device_by_id_sync" => get_device_by_id_sync(params),
        "get_sub_device_list_by_parent_id" => get_sub_device_list_by_parent_id(params),
        "get_sub_device_list_by_sub_device_id" => get_sub_device_list_by_sub_device_id(params),
        "get_device_info_list_by_family_id" => get_device_info_list_by_family_id(params),
        "get_device_info_by_id" => get_device_info_by_id(params),
        "unsubscribe_device_list_change" => unsubscribe_device_list_change(params),
        "unsubscribe_device_change" => unsubscribe_device_change(params),
        "unsubscribe_device_change_by_family_id" => unsubscribe_device_change_by_family_id(params),
        "unsubscribe_device_change_by_device_ids" => {
            unsubscribe_device_change_by_device_ids(params)
        }
        "clean_operate_serial_queue" => clean_operate_serial_queue(params),
        "priority_prepare_device" => priority_prepare_device(params),
        "is_group" => is_group(params),
        _ => {
            warn!("updevice: unsupported action: {}", action);
            invalid_arg_result("unsupported action")
        }
    }
}

pub async fn lib_updevice_cross_platform_async(params: HashMap<String, String>) -> Vec<u8> {
    let action = params.get(ACTION).unwrap_or(&EMPTY).as_str();
    info!("updevice: executing action: {}", action);
    match action {
        "get_device_list_by_family_id" => get_device_list_by_family_id(params).await,
        "get_device_by_id" => get_device_by_id(params).await,
        "update_device_list" => update_device_list(params).await,
        "execute_command" => execute_command(params).await,
        "get_initial_attribute_list" => get_initial_attribute_list(params).await,
        "get_attribute_list" => get_attribute_list(params).await,
        "get_attribute_by_name" => get_attribute_by_name(params).await,
        "get_caution_list" => get_caution_list(params).await,
        "get_modifier_config" => get_modifier_config(params).await,
        "get_business_function_list" => get_business_function_list(params).await,
        "operate" => operate(params).await,
        "create_device_group" => create_device_group(params).await,
        "find_groupable_devices" => find_groupable_devices(params).await,
        "delete_device_group" => delete_device_group(params).await,
        "add_devices_to_group" => add_devices_to_group(params).await,
        "remove_devices_from_group" => remove_devices_from_group(params).await,
        "get_group_member_list" => get_group_member_list(params).await,
        _ => {
            warn!("updevice: unsupported action: {}", action);
            invalid_arg_result("unsupported action")
        }
    }
}

pub fn lib_updevice_cross_platform_consumer_data(
    params: HashMap<String, String>,
    consumer: impl PlatformConsumer + 'static,
) -> Vec<u8> {
    let action = params.get(ACTION).unwrap_or(&EMPTY).as_str();
    info!("updevice: executing action: {}", action);
    match action {
        "subscribe_device_list_change" => subscribe_device_list_change(params, consumer),
        "subscribe_device_change" => subscribe_device_change(params, consumer),
        "subscribe_device_change_by_family_id" => {
            subscribe_device_change_by_family_id(params, consumer)
        }
        "subscribe_device_change_by_device_ids" => {
            subscribe_device_change_by_device_ids(params, consumer)
        }
        "operate_command_queue" => operate_command_queue(params, consumer),
        _ => {
            warn!("updevice: unsupported action: {}", action);
            invalid_arg_result("unsupported action")
        }
    }
}

fn get_device_manager() -> &'static UpDeviceManager {
    UpDeviceInjection::get_instance().get_device_manager()
}

fn init(params: HashMap<String, String>) -> Vec<u8> {
    require_params!(
        params,
        "app_id",
        "app_key",
        "app_version",
        "app_debug_mode",
        "client_id",
        "environment"
    );
    #[cfg(any(feature = "android", feature = "ohos", feature = "ios"))]
    {
        use rust_usdk::toolkit_ffi::fota::lib_fota::LibFotaImpl;
        use rust_usdk::toolkit_ffi::p2p_resource::lib_p2p_resource::LibP2PResourceImpl;
        use rust_usdk::toolkit_ffi::uhsd::LibUHomeImpl;
        use rust_usdk::toolkit_ffi::uhsd_bind::bind_impl::LibBindImpl;
        use rust_usdk::toolkit_ffi::uhsd_bind::by_qr_code_auth::lib_qrcode_auth::LibQRCodeAuthImpl;
        use rust_usdk::toolkit_ffi::uhsd_device_group::device_group_impl::LibDeviceGroupImpl;
        use rust_usdk::toolkit_ffi::uhsd_injection::UhsdInjection;
        use rust_usdk::usdk_toolkit::toolkit::{AppInfo, Area};
        UhsdInjection::get_instance().set_lib_uhome(Box::new(LibUHomeImpl::new()));
        UhsdInjection::get_instance().set_lib_bind(Box::new(LibBindImpl::new()));
        UhsdInjection::get_instance().set_lib_fota(Box::new(LibFotaImpl::new()));
        UhsdInjection::get_instance().set_lib_qrcode_auth(Box::new(LibQRCodeAuthImpl::new()));
        UhsdInjection::get_instance().set_lib_device_group(Box::new(LibDeviceGroupImpl::new()));
        UhsdInjection::get_instance().set_lib_resource(Box::new(LibP2PResourceImpl::new()));
        let app_id = params.get("app_id").unwrap().to_string();
        let app_key = params.get("app_key").unwrap().to_string();
        let app_version = params.get("app_version").unwrap().to_string();
        let app_debug_mode = params
            .get("app_debug_mode")
            .unwrap()
            .parse::<bool>()
            .unwrap();
        let client_id = params.get("client_id").unwrap().to_string();
        let environment = params.get("environment").unwrap().parse::<i32>().unwrap();
        UpDeviceInjection::get_instance().init_device_manager(
            Area::China,
            AppInfo::new(app_id, app_key, app_version, app_debug_mode),
            client_id,
            environment.into(),
        );
    }
    debug!("init success");
    void_result()
}

// API

fn get_device_info_list_by_family_id(params: HashMap<String, String>) -> Vec<u8> {
    require_params!(params, "family_id");
    let family_id = params.get("family_id").unwrap().to_string();
    let mut builder = FlatBufferBuilder::with_capacity(DEFAULT_SIZE);
    let device_model_list = get_device_manager()
        .get_device_info_list(&family_id)
        .iter()
        .map(|it| create_fbs_device_info(&mut builder, it))
        .collect::<Vec<_>>();
    let fbs_device_model_list = builder.create_vector(&device_model_list);
    let fbs_devices = FBSDeviceInfoList::create(
        &mut builder,
        &FBSDeviceInfoListArgs {
            device_infos: Some(fbs_device_model_list),
        },
    );
    data_result(
        &mut builder,
        fbs_devices.as_union_value(),
        UpDeviceContainer::FBSDeviceInfoList,
    )
}

fn get_device_by_id_sync(params: HashMap<String, String>) -> Vec<u8> {
    require_params!(params, "device_id");
    let device_id = params.get("device_id").unwrap();
    let mut builder = FlatBufferBuilder::with_capacity(DEFAULT_SIZE);
    match get_device_manager().get_device(device_id) {
        Some(device) => {
            let fbs_device = create_fbs_device_model_by_updevice_sync(&mut builder, &device);
            data_result(
                &mut builder,
                fbs_device.as_union_value(),
                UpDeviceContainer::FBSDeviceModel,
            )
        }
        None => no_device_result(device_id),
    }
}

async fn get_device_by_id(params: HashMap<String, String>) -> Vec<u8> {
    require_params!(params, "device_id");
    let device_id = params.get("device_id").unwrap();
    let with_engine_attributes = params
        .get("with_engine_attributes")
        .map_or(true, |v| v.parse::<bool>().unwrap_or(true));
    let mut builder = FlatBufferBuilder::with_capacity(DEFAULT_SIZE);
    match get_device_manager().get_device(device_id) {
        Some(device) => {
            let fbs_device = if with_engine_attributes {
                create_fbs_device_model_by_updevice(&mut builder, &device).await
            } else {
                create_fbs_device_model_by_updevice_sync(&mut builder, &device)
            };
            data_result(
                &mut builder,
                fbs_device.as_union_value(),
                UpDeviceContainer::FBSDeviceModel,
            )
        }
        None => no_device_result(device_id),
    }
}

fn get_device_info_by_id(params: HashMap<String, String>) -> Vec<u8> {
    require_params!(params, "device_id");
    let device_id = params.get("device_id").unwrap().to_string();
    let mut builder = FlatBufferBuilder::with_capacity(DEFAULT_SIZE);
    match get_device_manager().get_device_info(device_id.as_str()) {
        Some(device_info) => {
            let fbs_device_info = create_fbs_device_info(&mut builder, &device_info);
            data_result(
                &mut builder,
                fbs_device_info.as_union_value(),
                UpDeviceContainer::FBSDeviceInfo,
            )
        }
        None => no_device_result(&device_id),
    }
}

async fn get_device_list_by_family_id(params: HashMap<String, String>) -> Vec<u8> {
    require_params!(params, "family_id");
    let family_id = params.get("family_id").unwrap().to_string();
    let with_engine_attributes = params
        .get("with_engine_attributes")
        .map_or(true, |v| v.parse::<bool>().unwrap_or(true));
    let mut builder = FlatBufferBuilder::with_capacity(DEFAULT_SIZE);
    let mut device_model_list = Vec::new();
    let devices = get_device_manager().get_device_list_by_family_id(family_id);
    if with_engine_attributes {
        push_fbs_device_model_with_engine(&mut builder, &mut device_model_list, &devices).await;
    } else {
        push_fbs_device_model_without_engine(&mut builder, &mut device_model_list, &devices)
    }
    let fbs_device_model_list = builder.create_vector(&device_model_list);
    let fbs_devices = FBSDeviceModelList::create(
        &mut builder,
        &FBSDeviceModelListArgs {
            devices: Some(fbs_device_model_list),
        },
    );
    data_result(
        &mut builder,
        fbs_devices.as_union_value(),
        UpDeviceContainer::FBSDeviceModelList,
    )
}

fn get_sub_device_list_by_parent_id(params: HashMap<String, String>) -> Vec<u8> {
    require_params!(params, "parent_id");
    let parent_id = params.get("parent_id").unwrap().to_string();
    let mut builder = FlatBufferBuilder::with_capacity(DEFAULT_SIZE);
    let mut device_model_list = Vec::new();
    let devices = get_device_manager().get_sub_device_list_by_parent_id(parent_id.as_str());
    push_fbs_device_model_without_engine(&mut builder, &mut device_model_list, &devices);
    let fbs_device_model_list = builder.create_vector(&device_model_list);
    let fbs_devices = FBSDeviceModelList::create(
        &mut builder,
        &FBSDeviceModelListArgs {
            devices: Some(fbs_device_model_list),
        },
    );
    data_result(
        &mut builder,
        fbs_devices.as_union_value(),
        UpDeviceContainer::FBSDeviceModelList,
    )
}

fn get_sub_device_list_by_sub_device_id(params: HashMap<String, String>) -> Vec<u8> {
    require_params!(params, "sub_device_id");
    let sub_device_id = params.get("sub_device_id").unwrap().to_string();
    let mut builder = FlatBufferBuilder::with_capacity(DEFAULT_SIZE);
    let mut device_model_list = Vec::new();
    let devices = get_device_manager().get_sub_device_list_by_sub_device_id(sub_device_id.as_str());
    push_fbs_device_model_without_engine(&mut builder, &mut device_model_list, &devices);
    let fbs_device_model_list = builder.create_vector(&device_model_list);
    let fbs_devices = FBSDeviceModelList::create(
        &mut builder,
        &FBSDeviceModelListArgs {
            devices: Some(fbs_device_model_list),
        },
    );
    data_result(
        &mut builder,
        fbs_devices.as_union_value(),
        UpDeviceContainer::FBSDeviceModelList,
    )
}

// 订阅相关API

fn subscribe_device_list_change(
    params: HashMap<String, String>,
    consumer: impl PlatformConsumer + 'static,
) -> Vec<u8> {
    let immediate = params
        .get("immediate")
        .map_or(false, |v| v.parse::<bool>().unwrap_or(false));
    let observer = move |event| {
        let mut builder = FlatBufferBuilder::with_capacity(DEFAULT_SIZE);
        let (code, container_type, container) = match event {
            UpDeviceEvent::DeviceListChanged(_) => (
                1,
                UpDeviceContainer::NoneWrapper,
                Some(create_fbs_none(&mut builder).as_union_value()),
            ),
            _ => {
                return;
            }
        };
        let updevice_message = FBSUpDeviceMessage::create(
            &mut builder,
            &FBSUpDeviceMessageArgs {
                code,
                container_type,
                container,
            },
        );
        builder.finish(updevice_message, None);
        let data = builder.finished_data().to_vec();
        consumer.accept(data);
    };
    let observer_id = get_device_manager().subscribe_device_list_change(immediate, observer);
    string_result(observer_id)
}

fn unsubscribe_device_list_change(params: HashMap<String, String>) -> Vec<u8> {
    require_params!(params, "subscription_id");
    let subscription_id = params.get("subscription_id").unwrap().to_string();
    get_device_manager().unsubscribe_device_list_change(subscription_id.as_str());
    void_result()
}

fn create_boxed_observer(
    consumer: impl PlatformConsumer + 'static,
) -> Box<dyn Fn(UpDeviceEvent) + Send + Sync> {
    Box::new(move |event| {
        let mut builder = FlatBufferBuilder::with_capacity(DEFAULT_SIZE);
        let (code, container_type, container) = match event {
            UpDeviceEvent::AttributeChanged { device_id } => {
                let fbs_device_id = builder.create_string(&device_id);
                let list = get_device_manager()
                    .get_device(device_id.as_str())
                    .map_or(vec![], |it| it.get_attribute_list())
                    .iter()
                    .map(|it| create_fbs_device_attribute(&mut builder, it))
                    .collect::<Vec<_>>();
                let fbs_list = builder.create_vector(&list);
                let fbs_attrs = FBSAttributeChanged::create(
                    &mut builder,
                    &FBSAttributeChangedArgs {
                        device_id: Some(fbs_device_id),
                        attribute_list: Some(fbs_list),
                    },
                );
                (
                    1,
                    UpDeviceContainer::FBSAttributeChanged,
                    Some(fbs_attrs.as_union_value()),
                )
            }
            UpDeviceEvent::CautionChanged { device_id } => {
                let fbs_device_id = builder.create_string(&device_id);
                let list = get_device_manager()
                    .get_device(device_id.as_str())
                    .map_or(vec![], |it| it.get_caution_list())
                    .iter()
                    .map(|it| create_fbs_device_caution(&mut builder, it))
                    .collect::<Vec<_>>();
                let fbs_list = builder.create_vector(&list);
                let fbs_cautions = FBSCautionChanged::create(
                    &mut builder,
                    &FBSCautionChangedArgs {
                        device_id: Some(fbs_device_id),
                        caution_list: Some(fbs_list),
                    },
                );
                (
                    2,
                    UpDeviceContainer::FBSCautionChanged,
                    Some(fbs_cautions.as_union_value()),
                )
            }
            UpDeviceEvent::ConnectStateChanged { device_id } => {
                let fbs_device_id = builder.create_string(&device_id);
                let state: i32 = get_device_manager()
                    .get_device(device_id.as_str())
                    .map_or(UpDeviceConnectState::default(), |it| it.get_connect_state())
                    .into();
                let fbs_common_state = FBSCommonStateChanged::create(
                    &mut builder,
                    &FBSCommonStateChangedArgs {
                        device_id: Some(fbs_device_id),
                        state: state as i64,
                    },
                );
                (
                    3,
                    UpDeviceContainer::FBSCommonStateChanged,
                    Some(fbs_common_state.as_union_value()),
                )
            }
            UpDeviceEvent::OnlineStateChanged { device_id } => {
                let fbs_device_id = builder.create_string(&device_id);
                let state: i32 = get_device_manager()
                    .get_device(device_id.as_str())
                    .map_or(UpDeviceOnlineState::default(), |it| it.get_online_state())
                    .into();
                let fbs_common_state = FBSCommonStateChanged::create(
                    &mut builder,
                    &FBSCommonStateChangedArgs {
                        device_id: Some(fbs_device_id),
                        state: state as i64,
                    },
                );
                (
                    4,
                    UpDeviceContainer::FBSCommonStateChanged,
                    Some(fbs_common_state.as_union_value()),
                )
            }
            UpDeviceEvent::BaseInfoChanged { device_id } => {
                let info = get_device_manager()
                    .get_device(device_id.as_str())
                    .map_or(UpDeviceInfo::default(), |it| it.get_info());
                let fbs_info = create_fbs_device_info(&mut builder, &info);
                (
                    5,
                    UpDeviceContainer::FBSDeviceInfo,
                    Some(fbs_info.as_union_value()),
                )
            }
            UpDeviceEvent::SleepStateChanged { device_id } => {
                let fbs_device_id = builder.create_string(&device_id);
                let state: i32 = get_device_manager()
                    .get_device(device_id.as_str())
                    .map_or(UpDeviceSleepState::default(), |it| it.get_sleep_state())
                    .into();
                let fbs_common_state = FBSCommonStateChanged::create(
                    &mut builder,
                    &FBSCommonStateChangedArgs {
                        device_id: Some(fbs_device_id),
                        state: state as i64,
                    },
                );
                (
                    6,
                    UpDeviceContainer::FBSCommonStateChanged,
                    Some(fbs_common_state.as_union_value()),
                )
            }
            UpDeviceEvent::OfflineCauseChanged { device_id } => {
                let fbs_device_id = builder.create_string(&device_id);
                let cause: i32 = get_device_manager()
                    .get_device(device_id.as_str())
                    .map_or(UpDeviceOfflineCause::default(), |it| it.get_offline_cause())
                    .into();
                let fbs_common_state = FBSCommonStateChanged::create(
                    &mut builder,
                    &FBSCommonStateChangedArgs {
                        device_id: Some(fbs_device_id),
                        state: cause as i64,
                    },
                );
                (
                    7,
                    UpDeviceContainer::FBSCommonStateChanged,
                    Some(fbs_common_state.as_union_value()),
                )
            }
            UpDeviceEvent::OfflineDaysChanged { device_id } => {
                let fbs_device_id = builder.create_string(&device_id);
                let days = get_device_manager()
                    .get_device(device_id.as_str())
                    .map_or(0, |it| it.get_offline_days());
                let fbs_common_state = FBSCommonStateChanged::create(
                    &mut builder,
                    &FBSCommonStateChangedArgs {
                        device_id: Some(fbs_device_id),
                        state: days as i64,
                    },
                );
                (
                    8,
                    UpDeviceContainer::FBSCommonStateChanged,
                    Some(fbs_common_state.as_union_value()),
                )
            }
            _ => {
                return;
            }
        };
        let updevice_message = FBSUpDeviceMessage::create(
            &mut builder,
            &FBSUpDeviceMessageArgs {
                code,
                container_type,
                container,
            },
        );
        builder.finish(updevice_message, None);
        let data = builder.finished_data().to_vec();
        consumer.accept(data);
    })
}

<<<<<<<
    match get_device_manager().get_device(&device_id) {
        None => {
            string_result("".to_string())
        }
        Some(device) => {
            let subscription_id = device.subscribe(observer);
            string_result(subscription_id)
        }
    }
=======
fn subscribe_device_change(
    params: HashMap<String, String>,
    consumer: impl PlatformConsumer + 'static,
) -> Vec<u8> {
    require_params!(params, "device_id");
    let device_id = params.get("device_id").unwrap().to_string();
    let observer = create_boxed_observer(consumer);
    let id = get_device_manager().subscribe_device_change_by_devices(vec![device_id], observer);
    string_result(id)
>>>>>>>
}

fn unsubscribe_device_change(params: HashMap<String, String>) -> Vec<u8> {
    require_params!(params, "device_id", "subscription_id");
    let device_id = params.get("device_id").unwrap().to_string();
    let subscription_id = params.get("subscription_id").unwrap().to_string();
<<<<<<<

    match get_device_manager().get_device(&device_id) {
        None => {
            bool_result(false)
        }
        Some(device) => {
            device.unsubscribe(&subscription_id);
            bool_result(true)
        }
    }
=======
    get_device_manager().unsubscribe_device_change_by_devices(subscription_id.as_str());
    void_result()
>>>>>>>
}

fn subscribe_device_change_by_family_id(
    params: HashMap<String, String>,
    consumer: impl PlatformConsumer + 'static,
) -> Vec<u8> {
    require_params!(params, "family_id");
    let family_id = params.get("family_id").unwrap().to_string();
    let observer = create_boxed_observer(consumer);
    let id = get_device_manager().subscribe_device_change_by_family_id(family_id, observer);
    string_result(id)
}

fn unsubscribe_device_change_by_family_id(params: HashMap<String, String>) -> Vec<u8> {
    require_params!(params, "subscription_id");
    let subscription_id = params.get("subscription_id").unwrap().to_string();
    get_device_manager().unsubscribe_device_change_by_family_id(subscription_id.as_str());
    void_result()
}

fn subscribe_device_change_by_device_ids(
    params: HashMap<String, String>,
    consumer: impl PlatformConsumer + 'static,
) -> Vec<u8> {
    require_params!(params, "device_ids");
    let json = params.get("device_ids").unwrap().to_string();
    let device_ids = serde_json::from_str::<Vec<String>>(json.as_str());
    if device_ids.is_err() {
        let error_info = device_ids.unwrap_err().to_string();
        return invalid_arg_result(error_info.as_str());
    }
    let device_id_list: Vec<String> = device_ids.unwrap();
    let observer = create_boxed_observer(consumer);
    let id = get_device_manager().subscribe_device_change_by_devices(device_id_list, observer);
    string_result(id)
}

fn unsubscribe_device_change_by_device_ids(params: HashMap<String, String>) -> Vec<u8> {
    require_params!(params, "subscription_id");
    let subscription_id = params.get("subscription_id").unwrap().to_string();
    get_device_manager().unsubscribe_device_change_by_devices(subscription_id.as_str());
    void_result()
}

// 更新数据API

async fn update_device_list(params: HashMap<String, String>) -> Vec<u8> {
    require_params!(params, "is_use_cache");
    let is_use_cache = params
        .get("is_use_cache")
        .map_or(false, |v| v.parse::<bool>().unwrap_or(false));
    let with_engine_attributes = params
        .get("with_engine_attributes")
        .map_or(true, |v| v.parse::<bool>().unwrap_or(true));
    let family_id: Option<String> = if let Some(temp_family_id) = params.get("family_id") {
        Some(temp_family_id.to_string())
    } else {
        None
    };
    match get_device_manager().update_device_list(is_use_cache).await {
        Ok(it) => {
            let mut devices = it;
            let mut builder = FlatBufferBuilder::with_capacity(DEFAULT_SIZE);
            if family_id.is_some() {
                devices = UpDeviceInjection::get_instance()
                    .get_device_manager()
                    .get_device_list_by_family_id(family_id.unwrap());
            }
            let mut device_model_list = Vec::new();
            for i in 0..devices.len() {
                let device = devices[i].clone();
                let fbs_device_model = if with_engine_attributes {
                    create_fbs_device_model_by_updevice(&mut builder, &device).await
                } else {
                    create_fbs_device_model_by_updevice_sync(&mut builder, &device)
                };
                device_model_list.push(fbs_device_model);
            }
            let fbs_device_model_list = builder.create_vector(&device_model_list);
            let fbs_devices = FBSDeviceModelList::create(
                &mut builder,
                &FBSDeviceModelListArgs {
                    devices: Some(fbs_device_model_list),
                },
            );
            data_result(
                &mut builder,
                fbs_devices.as_union_value(),
                UpDeviceContainer::FBSDeviceModelList,
            )
        }
        Err(e) => failure_result(&e.to_string(), "100"),
    }
}

// 下发命令

fn convert_device_feedback_to_vec(result: Result<DeviceFeedback>) -> Vec<u8> {
    match result {
        Ok(it) => {
            let mut builder = FlatBufferBuilder::with_capacity(DEFAULT_SIZE);
            let fbs_device_feedback = create_fbs_device_feedback(&mut builder, &it);
            data_result(
                &mut builder,
                fbs_device_feedback.as_union_value(),
                UpDeviceContainer::FBSDeviceFeedback,
            )
        }
        Err(e) => failure_result(&e.to_string(), DEVICE_ERROR),
    }
}

async fn execute_command(params: HashMap<String, String>) -> Vec<u8> {
    require_params!(params, "device_id", "pair_map");
    let device_id = params.get("device_id").unwrap().to_string();
    let pair_map_str = params.get("pair_map").unwrap().to_string();
    let mut pair_map: HashMap<String, String> = HashMap::new();
    let parse_json = serde_json::from_str(&pair_map_str);
    match parse_json {
        Ok(pari) => {
            pair_map = pari;
        }
        Err(e) => return invalid_arg_result(&e.to_string()),
    }
    debug!("execute_command before: {:?}", pair_map);
    let group_name = if let Some(temp_group_name) = params.get("group_name") {
        Some(temp_group_name.to_string())
    } else {
        None
    };
    let attributes = pair_map
        .iter()
        .map(|(k, v)| UpDeviceAttribute::new(k.to_owned(), v.to_owned()))
        .collect();
    let cmd = UpDeviceCommand::new(group_name, attributes);
    debug!("execute_command after: {:?}", cmd);
    match get_device_manager().get_device(device_id.as_str()) {
        None => no_device_result(&device_id),
        Some(device) => {
            let result = device.execute_command(cmd).await;
            convert_device_feedback_to_vec(result)
        }
    }
}

async fn operate(params: HashMap<String, String>) -> Vec<u8> {
    require_params!(params, "device_id", "commands");
    let device_id = params.get("device_id").unwrap();
    let commands_json = params.get("commands").unwrap();
    let command_list = serde_json::from_str::<Vec<Command>>(commands_json);
    if command_list.is_err() {
        let error_info = command_list.unwrap_err().to_string();
        return invalid_arg_result(error_info.as_str());
    }
    let device = get_device_manager().get_device(device_id);
    if device.is_none() {
        return no_device_result(device_id);
    }
    let result = device.unwrap().operate(command_list.unwrap()).await;
    convert_device_feedback_to_vec(result)
}

fn operate_command_queue(
    params: HashMap<String, String>,
    consumer: impl PlatformConsumer + 'static,
) -> Vec<u8> {
    require_params!(params, "device_id", "commands");
    let device_id = params.get("device_id").unwrap();
    let commands_json = params.get("commands").unwrap();
    let command_list = serde_json::from_str::<Vec<Command>>(commands_json);
    if command_list.is_err() {
        let error_info = command_list.unwrap_err().to_string();
        return invalid_arg_result(error_info.as_str());
    }
    let device = get_device_manager().get_device(device_id);
    if device.is_none() {
        return no_device_result(device_id);
    }
    device.unwrap().operate_command_queue(
        command_list.unwrap(),
        FnClone::new(move |it| {
            let vec_data = convert_device_feedback_to_vec(it);
            consumer.accept(vec_data);
        }),
    );
    void_result()
}

// ExtendApi

async fn get_initial_attribute_list(params: HashMap<String, String>) -> Vec<u8> {
    require_params!(params, "device_id");
    let device_id = params.get("device_id").unwrap().to_string();
    match get_device_manager().get_device(device_id.as_str()) {
        None => no_device_result(&device_id),
        Some(it) => {
            let extend_api = it.get_extend_api();
            let attr_list: Vec<Arc<LEAttribute>> = extend_api.query_initial_attributes().await;
            let mut builder = FlatBufferBuilder::with_capacity(DEFAULT_SIZE);
            let attrs = attr_list
                .iter()
                .map(|attr| create_fbs_attribute(&mut builder, &attr))
                .collect::<Vec<_>>();
            let fbs_attr_list = builder.create_vector(&attrs);
            let fbs_attrs = FBSAttributeList::create(
                &mut builder,
                &FBSAttributeListArgs {
                    attributes: Some(fbs_attr_list),
                },
            );
            data_result(
                &mut builder,
                fbs_attrs.as_union_value(),
                UpDeviceContainer::FBSAttributeList,
            )
        }
    }
}

async fn get_attribute_list(params: HashMap<String, String>) -> Vec<u8> {
    require_params!(params, "device_id");
    let device_id = params.get("device_id").unwrap().to_string();
    match get_device_manager().get_device(device_id.as_str()) {
        None => no_device_result(&device_id),
        Some(it) => {
            let extend_api = it.get_extend_api();
            let attr_list: Vec<Arc<LEAttribute>> = extend_api.query_attributes().await;
            let mut builder = FlatBufferBuilder::with_capacity(DEFAULT_SIZE);
            let attrs = attr_list
                .iter()
                .map(|attr| create_fbs_attribute(&mut builder, &attr))
                .collect::<Vec<_>>();
            let fbs_attr_list = builder.create_vector(&attrs);
            let fbs_attrs = FBSAttributeList::create(
                &mut builder,
                &FBSAttributeListArgs {
                    attributes: Some(fbs_attr_list),
                },
            );
            data_result(
                &mut builder,
                fbs_attrs.as_union_value(),
                UpDeviceContainer::FBSAttributeList,
            )
        }
    }
}

async fn get_attribute_by_name(params: HashMap<String, String>) -> Vec<u8> {
    require_params!(params, "device_id", "name");
    let device_id = params.get("device_id").unwrap().to_string();
    let name = params.get("name").unwrap().to_string();
    match get_device_manager().get_device(device_id.as_str()) {
        None => no_device_result(&device_id),
        Some(it) => {
            let extend_api = it.get_extend_api();
            let option_attr: Option<Arc<LEAttribute>> =
                extend_api.query_attribute_by_name(name.as_str()).await;
            let mut builder = FlatBufferBuilder::with_capacity(DEFAULT_SIZE);
            if let Some(attr) = option_attr {
                let fbs_attr = create_fbs_attribute(&mut builder, &attr);
                data_result(
                    &mut builder,
                    fbs_attr.as_union_value(),
                    UpDeviceContainer::FBSAttribute,
                )
            } else {
                no_attr_result(device_id.as_str(), name.as_str())
            }
        }
    }
}

async fn get_caution_list(params: HashMap<String, String>) -> Vec<u8> {
    require_params!(params, "device_id");
    let device_id = params.get("device_id").unwrap().to_string();
    match get_device_manager().get_device(device_id.as_str()) {
        None => no_device_result(&device_id),
        Some(it) => {
            let extend_api = it.get_extend_api();
            let caution_list: Vec<LECaution> = extend_api.query_cautions().await;
            let mut builder = FlatBufferBuilder::with_capacity(DEFAULT_SIZE);
            let cautions = caution_list
                .iter()
                .map(|caution| create_fbs_caution(&mut builder, caution))
                .collect::<Vec<_>>();
            let fbs_caution_list = builder.create_vector(&cautions);
            let fbs_cautions = FBSCautionList::create(
                &mut builder,
                &FBSCautionListArgs {
                    cautions: Some(fbs_caution_list),
                },
            );
            data_result(
                &mut builder,
                fbs_cautions.as_union_value(),
                UpDeviceContainer::FBSCautionList,
            )
        }
    }
}

async fn get_modifier_config(params: HashMap<String, String>) -> Vec<u8> {
    require_params!(params, "device_id");
    let device_id = params.get("device_id").unwrap().to_string();
    match get_device_manager().get_device(device_id.as_str()) {
        None => no_device_result(&device_id),
        Some(it) => {
            let extend_api = it.get_extend_api();
            let modifier_list: Vec<Modifier> = extend_api.query_modifier_configs().await;
            let mut builder = FlatBufferBuilder::with_capacity(DEFAULT_SIZE);
            let modifiers = modifier_list
                .iter()
                .map(|modifier| create_fbs_modifier(&mut builder, modifier))
                .collect::<Vec<_>>();
            let fbs_modifier_list = builder.create_vector(&modifiers);
            let fbs_modifiers = FBSModifierList::create(
                &mut builder,
                &FBSModifierListArgs {
                    modifier: Some(fbs_modifier_list),
                },
            );
            data_result(
                &mut builder,
                fbs_modifiers.as_union_value(),
                UpDeviceContainer::FBSModifierList,
            )
        }
    }
}

async fn get_business_function_list(params: HashMap<String, String>) -> Vec<u8> {
    require_params!(params, "device_id");
    let device_id = params.get("device_id").unwrap().to_string();
    match get_device_manager().get_device(device_id.as_str()) {
        None => no_device_result(&device_id),
        Some(it) => {
            let extend_api = it.get_extend_api();
            let business_attr_list: Vec<BusinessAttr> = extend_api.query_business_functions().await;
            let mut builder = FlatBufferBuilder::with_capacity(DEFAULT_SIZE);
            let business_attrs = business_attr_list
                .iter()
                .map(|business_attr| create_fbs_business_attr(&mut builder, business_attr))
                .collect::<Vec<_>>();
            let fbs_business_attr_list = builder.create_vector(&business_attrs);
            let fbs_business_attrs = FBSBusinessAttrList::create(
                &mut builder,
                &FBSBusinessAttrListArgs {
                    business_attrs: Some(fbs_business_attr_list),
                },
            );
            data_result(
                &mut builder,
                fbs_business_attrs.as_union_value(),
                UpDeviceContainer::FBSBusinessAttrList,
            )
        }
    }
}

async fn refresh_attributes(params: HashMap<String, String>) -> Vec<u8> {
    require_params!(params, "device_id");
    let device_id = params.get("device_id").unwrap().to_string();
    match get_device_manager().get_device(device_id.as_str()) {
        None => no_device_result(&device_id),
        Some(it) => {
            it.refresh_attributes().await;
            void_result()
        }
    }
}

fn clean_operate_serial_queue(params: HashMap<String, String>) -> Vec<u8> {
    require_params!(params, "device_id", "name");
    let device_id = params.get("device_id").unwrap().to_string();
    match get_device_manager().get_device(device_id.as_str()) {
        None => no_device_result(&device_id),
        Some(it) => {
            it.clear_command_queue();
            void_result()
        }
    }
}

fn priority_prepare_device(params: HashMap<String, String>) -> Vec<u8> {
    require_params!(params, "device_id", "name");
    let device_id = params.get("device_id").unwrap().to_string();
    get_device_manager().priority_prepare_device(&device_id);
    void_result()
}

// device group API

async fn create_device_group(params: HashMap<String, String>) -> Vec<u8> {
    require_params!(params, "device_id");
    let device_id = params.get("device_id").unwrap().to_string();
    let trace_id = UhsdManager::get_instance()
        .get_bind()
        .create_trace_id()
        .unwrap_or_default();
    let req_sn = UhsdManager::get_instance().get_common().get_request_sn() as u32;
    debug!(
        "device_group_log cmd_create_device_group device_id:{:?}, trace_id:{}, req_sn:{}",
        device_id, trace_id, req_sn,
    );
    let ret = UhsdManager::get_instance()
        .get_device_group()
        .create_device_group(GroupDeviceReqInfo {
            device_id,
            trace_id,
            req_sn,
            timeout: 60,
        })
        .await;
    debug!("device_group_log:cmd_create_device_group result: {:?}", ret);
    void_result()
}

fn is_group(params: HashMap<String, String>) -> Vec<u8> {
    require_params!(params, "device_id");
    let device_id = params.get("device_id").unwrap().to_string();
    let result = UhsdManager::get_instance()
        .get_device_group()
        .is_group(device_id);
    let is_group = result.unwrap_or(false);
    bool_result(is_group)
}

fn convert_uhsd_pair_list_to_vec(pair_list: Vec<UhsdPair>, filter_field: &str) -> Vec<u8> {
    match pair_list.iter().find(|it| it.name.eq(filter_field)) {
        None => failure_result(
            format!("no {} in uhsd_pair", filter_field).as_str(),
            GROUPABLE_DEVICES_ERROR,
        ),
        Some(it) => {
            let mut builder = FlatBufferBuilder::with_capacity(DEFAULT_SIZE);
            let name = builder.create_string(it.name.as_str());
            let value = builder.create_string(it.value.as_str());
            let fbs_pair = FBSUhsdPair::create(
                &mut builder,
                &FBSUhsdPairArgs {
                    name: Some(name),
                    value: Some(value),
                },
            );
            data_result(
                &mut builder,
                fbs_pair.as_union_value(),
                UpDeviceContainer::FBSUhsdPair,
            )
        }
    }
}

async fn find_groupable_devices(params: HashMap<String, String>) -> Vec<u8> {
    require_params!(params, "device_id");
    let device_id = params.get("device_id").unwrap().to_string();
    let req_sn = UhsdManager::get_instance().get_common().get_request_sn() as u32;
    let trace_id = UhsdManager::get_instance()
        .get_bind()
        .create_trace_id()
        .unwrap_or_default();
    let result = UhsdManager::get_instance()
        .get_device_group()
        .find_groupable_devices(GroupDeviceBaseReqInfo {
            base_device_id: device_id,
            trace_id,
            req_sn,
        })
        .await;
    if result.is_err() {
        let error_info = result.unwrap_err().to_string();
        return failure_result(error_info.as_str(), GROUPABLE_DEVICES_ERROR);
    }
    convert_uhsd_pair_list_to_vec(result.unwrap(), "device_ids")
}

async fn delete_device_group(params: HashMap<String, String>) -> Vec<u8> {
    require_params!(params, "device_id");
    let device_id = params.get("device_id").unwrap().to_string();
    let trace_id = UhsdManager::get_instance()
        .get_bind()
        .create_trace_id()
        .unwrap_or_default();
    let req_sn = UhsdManager::get_instance().get_common().get_request_sn() as u32;
    let result = UhsdManager::get_instance()
        .get_device_group()
        .delete_device_group(GroupDeviceReqInfo {
            device_id,
            trace_id,
            req_sn,
            timeout: UHSD_DEFAULT_GROUP_DEVICE_TIMEOUT as u32,
        })
        .await;
    if result.is_err() {
        let error_info = result.unwrap_err().to_string();
        return failure_result(error_info.as_str(), GROUPABLE_DEVICES_ERROR);
    }
    convert_uhsd_pair_list_to_vec(result.unwrap(), "device_id")
}

async fn add_devices_to_group(params: HashMap<String, String>) -> Vec<u8> {
    require_params!(params, "device_id", "device_ids");
    let device_id = params.get("device_id").unwrap().to_string();
    let device_ids_json = params.get("device_ids").unwrap();
    let device_ids = serde_json::from_str::<Vec<String>>(device_ids_json);
    if device_ids.is_err() {
        let error_info = device_ids.unwrap_err().to_string();
        return invalid_arg_result(error_info.as_str());
    }
    let device_ids = device_ids.unwrap();
    let length = device_ids.len();
    let req_sn = UhsdManager::get_instance().get_common().get_request_sn() as u32;
    let trace_id = UhsdManager::get_instance()
        .get_bind()
        .create_trace_id()
        .unwrap_or_default();
    let result = UhsdManager::get_instance()
        .get_device_group()
        .add_devices_to_group(GroupDeviceAddDelReqInfo {
            group_device_id: device_id,
            device_ids,
            trace_id,
            num: length as u32,
            req_sn,
            timeout: DEVICE_GROUP_TIMEOUT_ADD_DEL_DEVICES as u32,
        })
        .await;
    if result.is_err() {
        let error_info = result.unwrap_err().to_string();
        return failure_result(error_info.as_str(), GROUPABLE_DEVICES_ERROR);
    }
    convert_uhsd_pair_list_to_vec(result.unwrap(), "add_del_result")
}

async fn remove_devices_from_group(params: HashMap<String, String>) -> Vec<u8> {
    require_params!(params, "device_id", "device_ids");
    let device_id = params.get("device_id").unwrap().to_string();
    let device_ids_json = params.get("device_ids").unwrap();
    let device_ids = serde_json::from_str::<Vec<String>>(device_ids_json);
    if device_ids.is_err() {
        let error_info = device_ids.unwrap_err().to_string();
        return invalid_arg_result(error_info.as_str());
    }
    let device_ids = device_ids.unwrap();
    let length = device_ids.len();
    let req_sn = UhsdManager::get_instance().get_common().get_request_sn() as u32;
    let trace_id = UhsdManager::get_instance()
        .get_bind()
        .create_trace_id()
        .unwrap_or_default();
    let result = UhsdManager::get_instance()
        .get_device_group()
        .remove_devices_from_group(GroupDeviceAddDelReqInfo {
            group_device_id: device_id,
            device_ids,
            trace_id,
            num: length as u32,
            req_sn,
            timeout: DEVICE_GROUP_TIMEOUT_ADD_DEL_DEVICES as u32,
        })
        .await;
    if result.is_err() {
        let error_info = result.unwrap_err().to_string();
        return failure_result(error_info.as_str(), GROUPABLE_DEVICES_ERROR);
    }
    convert_uhsd_pair_list_to_vec(result.unwrap(), "add_del_result")
}

async fn get_group_member_list(params: HashMap<String, String>) -> Vec<u8> {
    require_params!(params, "device_id");
    let device_id = params.get("device_id").unwrap();
    let usdk_member_list = UhsdManager::get_instance()
        .get_device_group()
        .get_group_member_list(device_id.to_string());
    if usdk_member_list.is_err() {
        let error_info = usdk_member_list.unwrap_err().to_string();
        return failure_result(
            format!("get_group_member_list: {}", error_info).as_str(),
            NO_DEVICE_ERROR,
        );
    }
    let usdk_members = usdk_member_list.unwrap_or_default();
    let group_device = UpDeviceInjection::get_instance()
        .get_device_manager()
        .get_device_info(device_id);
    if group_device.is_none() {
        return failure_result(device_id, NO_DEVICE_ERROR);
    }
    let group_device = group_device.unwrap();
    let family_id = group_device.get_relation().family_id();
    let family = UserDomainManager::get_instance()
        .get_user_domain()
        .get_user()
        .get_family_by_id(family_id.as_str());
    if family.is_none() {
        return failure_result("family not found", NO_FAMILY_ERROR);
    }
    let family = family.unwrap();
    let group_devices = family.get_group_device_list().await;
    if group_devices.is_err() {
        let error_info = group_devices.unwrap_err().to_string();
        return failure_result(
            format!("group_devices not found: {}", error_info).as_str(),
            NO_DEVICE_ERROR,
        );
    }
    let group_devices = group_devices.unwrap();
    let mut builder = FlatBufferBuilder::with_capacity(DEFAULT_SIZE);
    let fbs_device_model_list = group_devices
        .into_iter()
        .filter(|it| usdk_members.contains(&it.device_info.device_id))
        .map(|it| {
            // lib params:
            let updevice_info: UpDeviceInfo = it.device_info.into();
            let uhsd_device_info = UhsdManager::get_instance()
                .get_device()
                .get_device_info(device_id);
            // fbs field:
            let device_id = &updevice_info.device_id();
            let protocol = builder.create_string(&updevice_info.protocol());
            let fbs_device_id = builder.create_string(&updevice_info.device_id());
            let fbs_updevice_info = create_fbs_device_info(&mut builder, &updevice_info);
            let attrs_list = UhsdManager::get_instance()
                .get_device()
                .get_device_attribute_list(device_id)
                .unwrap_or(vec![])
                .iter()
                .map(|attr| create_fbs_device_attribute_by_uhsd(&mut builder, attr))
                .collect::<Vec<_>>();
            let fbs_attrs_vec = builder.create_vector(&attrs_list);
            let config_state = builder.create_string(&UpDeviceConfigState::default().to_string());
            let ble_state = builder.create_string(&UpDeviceConnectState::default().to_string());
            let mut connect_state = UpDeviceConnectState::default();
            let mut online_state = UpDeviceOnlineState::default();
            let mut sleep_state = UpDeviceSleepState::default();
            let mut state_code = 0;
            let mut offline_cause = UpDeviceOfflineCause::default();
            let mut offline_days = 0;
            if uhsd_device_info.is_ok() {
                let usdk_info = uhsd_device_info.unwrap();
                connect_state = UpDeviceConnectState::from(&usdk_info.connect_state);
                online_state = UpDeviceOnlineState::from(&usdk_info.online_state);
                sleep_state = UpDeviceSleepState::from(&usdk_info.sleep_state);
                state_code = usdk_info.state_code;
                offline_cause = UpDeviceOfflineCause::from(&usdk_info.offline_cause);
                offline_days = usdk_info.offline_days;
            }
            let fbs_connect_state = builder.create_string(&connect_state.to_string());
            let fbs_online_state = builder.create_string(&online_state.to_string());
            let fbs_sleep_state = builder.create_string(&sleep_state.to_string());
            let fbs_offline_cause = builder.create_string(&offline_cause.to_string());
            FBSDeviceModel::create(
                &mut builder,
                &FBSDeviceModelArgs {
                    protocol: Some(protocol),
                    device_id: Some(fbs_device_id),
                    device_info: Some(fbs_updevice_info),
                    attribute_list: Some(fbs_attrs_vec),
                    caution_list: None,
                    engine_attribute_list: None,
                    engine_caution_list: None,
                    config_state: Some(config_state),
                    connect_state: Some(fbs_connect_state),
                    ble_state: Some(ble_state),
                    online_state: Some(fbs_online_state),
                    sleep_state: Some(fbs_sleep_state),
                    state_code: state_code as i32,
                    offline_cause: Some(fbs_offline_cause),
                    offline_days,
                },
            )
        })
        .collect::<Vec<_>>();
    let fbs_device_model_vec = builder.create_vector(&fbs_device_model_list);
    let fbs = FBSDeviceModelList::create(
        &mut builder,
        &FBSDeviceModelListArgs {
            devices: Some(fbs_device_model_vec),
        },
    );
    data_result(
        &mut builder,
        fbs.as_union_value(),
        UpDeviceContainer::FBSDeviceModelList,
    )
}

// Create Model

fn push_fbs_device_model_without_engine<'a>(
    builder: &mut FlatBufferBuilder<'a>,
    device_model_list: &mut Vec<WIPOffset<FBSDeviceModel<'a>>>,
    devices: &Vec<Arc<dyn UpDevice>>,
) {
    let length = devices.len();
    for i in 0..length {
        let device = devices.get(i).unwrap();
        let model = create_fbs_device_model_by_updevice_sync(builder, &device);
        device_model_list.push(model);
    }
}

async fn push_fbs_device_model_with_engine<'a>(
    builder: &mut FlatBufferBuilder<'a>,
    device_model_list: &mut Vec<WIPOffset<FBSDeviceModel<'a>>>,
    devices: &Vec<Arc<dyn UpDevice>>,
) {
    let length = devices.len();
    for i in 0..length {
        let device = devices.get(i).unwrap();
        let model = create_fbs_device_model_by_updevice(builder, &device).await;
        device_model_list.push(model);
    }
}

fn create_fbs_device_model_by_updevice_sync<'a>(
    builder: &mut FlatBufferBuilder<'a>,
    device: &Arc<dyn UpDevice>,
) -> WIPOffset<FBSDeviceModel<'a>> {
    let args = create_fbs_device_model_args(builder, device);
    FBSDeviceModel::create(builder, &args)
}

async fn create_fbs_device_model_by_updevice<'a>(
    builder: &mut FlatBufferBuilder<'a>,
    device: &Arc<dyn UpDevice>,
) -> WIPOffset<FBSDeviceModel<'a>> {
    let mut args = create_fbs_device_model_args(builder, device);
    let extend_api = device.get_extend_api();
    let engine_attribute_list = extend_api
        .query_attributes()
        .await
        .iter()
        .map(|it| create_fbs_attribute(builder, it))
        .collect::<Vec<_>>();
    let engine_caution_list = extend_api
        .query_cautions()
        .await
        .iter()
        .map(|it| create_fbs_caution(builder, it))
        .collect::<Vec<_>>();
    args.engine_attribute_list = Some(builder.create_vector(&engine_attribute_list));
    args.engine_caution_list = Some(builder.create_vector(&engine_caution_list));
    FBSDeviceModel::create(builder, &args)
}

fn create_fbs_device_model_args<'a>(
    builder: &mut FlatBufferBuilder<'a>,
    device: &Arc<dyn UpDevice>,
) -> FBSDeviceModelArgs<'a> {
    let protocol = builder.create_string(&device.protocol());
    let device_id = builder.create_string(&device.device_id());
    let device_info = create_fbs_device_info(builder, &device.get_device_core().get_device_info());
    let attribute_list = device
        .get_device_core()
        .get_attribute_list()
        .iter()
        .map(|it| create_fbs_device_attribute(builder, it))
        .collect::<Vec<_>>();
    let fbs_attribute_list = builder.create_vector(&attribute_list);
    let caution_list = device
        .get_device_core()
        .get_caution_list()
        .iter()
        .map(|it| create_fbs_device_caution(builder, it))
        .collect::<Vec<_>>();
    let fbs_caution_list = builder.create_vector(&caution_list);
    let config_state = builder.create_string(&device.get_config_state().to_string());
    let connect_state = builder.create_string(&device.get_connect_state().to_string());
    let ble_state = builder.create_string(&device.get_ble_state().to_string());
    let online_state = builder.create_string(&device.get_online_state().to_string());
    let sleep_state = builder.create_string(&device.get_sleep_state().to_string());
    let offline_cause = builder.create_string(&device.get_offline_cause().to_string());
    FBSDeviceModelArgs {
        protocol: Some(protocol),
        device_id: Some(device_id),
        device_info: Some(device_info),
        attribute_list: Some(fbs_attribute_list),
        caution_list: Some(fbs_caution_list),
        engine_attribute_list: None,
        engine_caution_list: None,
        config_state: Some(config_state),
        connect_state: Some(connect_state),
        ble_state: Some(ble_state),
        online_state: Some(online_state),
        sleep_state: Some(sleep_state),
        state_code: device.get_state_code(),
        offline_cause: Some(offline_cause),
        offline_days: device.get_offline_days(),
    }
}

fn create_fbs_option_string<'a>(
    builder: &mut FlatBufferBuilder<'a>,
    option_string: &Option<String>,
) -> Option<WIPOffset<&'a str>> {
    option_string.as_ref().map(|it| builder.create_string(it))
}

fn create_fbs_device_info<'a>(
    builder: &mut FlatBufferBuilder<'a>,
    device: &UpDeviceInfo,
) -> WIPOffset<FBSDeviceInfo<'a>> {
    let protocol = builder.create_string(&device.protocol());
    let device_id = builder.create_string(&device.device_id());
    let type_id = create_fbs_option_string(builder, &device.type_id());
    let type_code = create_fbs_option_string(builder, &device.type_code());
    let model = builder.create_string(&device.model());
    let product_code = builder.create_string(&device.product_code());
    let parent_id = create_fbs_option_string(builder, &device.parent_id());
    let base_info = FBSDeviceBaseInfo::create(
        builder,
        &FBSDeviceBaseInfoArgs {
            protocol: Some(protocol),
            device_id: Some(device_id),
            type_id,
            type_code,
            model: Some(model),
            product_code: Some(product_code),
            parent_id,
        },
    );
    let basic = device.get_basic();
    let display_name = builder.create_string(&basic.display_name());
    let app_type_name = builder.create_string(&basic.app_type_name());
    let app_type_code = builder.create_string(&basic.app_type_code());
    let room_name = builder.create_string(&basic.room_name());
    let room_id = builder.create_string(&basic.room_id());
    let sub_device_ids = basic
        .sub_device_ids()
        .iter()
        .map(|it| builder.create_string(it))
        .collect::<Vec<_>>();
    let fbs_sub_device_ids = builder.create_vector(&sub_device_ids);
    let bind_time = builder.create_string(&basic.bind_time());
    let device_aggregate_type = builder.create_string(&basic.device_aggregate_type());
    let attachment_sort_code = basic.attachment_sort_code();
    let device_role = create_fbs_option_string(builder, &basic.device_role());
    let device_role_type = create_fbs_option_string(builder, &basic.device_role_type());
    let device_net_type = create_fbs_option_string(builder, &basic.device_net_type());
    let device_group_id = create_fbs_option_string(builder, &basic.device_group_id());
    let device_group_type = create_fbs_option_string(builder, &basic.device_group_type());
    let device_basic = FBSDeviceBasic::create(
        builder,
        &FBSDeviceBasicArgs {
            display_name: Some(display_name),
            app_type_name: Some(app_type_name),
            app_type_code: Some(app_type_code),
            room_name: Some(room_name),
            room_id: Some(room_id),
            online: basic.online(),
            sub_device_ids: Some(fbs_sub_device_ids),
            bind_time: Some(bind_time),
            device_aggregate_type: Some(device_aggregate_type),
            attachment_sort_code: attachment_sort_code as i64,
            device_role,
            device_role_type,
            device_net_type,
            device_group_id,
            device_group_type,
        },
    );
    let permission = device.get_permission();
    let auth_type = builder.create_string(&permission.auth_type());
    let device_permission = FBSDevicePermission::create(
        builder,
        &FBSDevicePermissionArgs {
            auth_type: Some(auth_type),
            is_controllable: permission.is_controllable(),
            is_editable: permission.is_editable(),
            is_viewable: permission.is_viewable(),
        },
    );
    let product = device.get_product();
    let bar_code = create_fbs_option_string(builder, &product.bar_code());
    let brand = builder.create_string(&product.brand());
    let category = builder.create_string(&product.category());
    let category_code = builder.create_string(&product.category_code());
    let category_grouping = builder.create_string(&product.category_grouping());
    let two_grouping_name = builder.create_string(product.two_grouping_name());
    let device_type = create_fbs_option_string(builder, &product.device_type());
    let image_url = builder.create_string(&product.image_url());
    let bind_type = create_fbs_option_string(builder, &product.bind_type());
    let access_type = create_fbs_option_string(builder, &product.access_type());
    let communication_mode = create_fbs_option_string(builder, &product.communication_mode());
    let config_type = builder.create_string(&product.config_type());
    let app_type_icon = builder.create_string(&product.app_type_icon());
    let card_page_icon = builder.create_string(product.card_page_icon());
    let card_page_img = builder.create_string(product.card_page_img());
    let card_sort = product.card_sort();
    let card_status = product.card_status();
    let aggregation_parent_id = builder.create_string(&product.aggregation_parent_id());
    let support_aggregation_flag = product.support_aggregation_flag();
    let shared_device_flag = product.shared_device_flag();
    let card_info_map = product
        .card_info_map()
        .iter()
        .map(|it| {
            let key_ = builder.create_string(it.0);
            let share_info_ = it.1;
            let family_id_ = builder.create_string(&share_info_.family_id);
            let fbs_share_card_info = FBSDeviceShareCardInfo::create(
                builder,
                &FBSDeviceShareCardInfoArgs {
                    family_id: Some(family_id_),
                    card_sort: share_info_.card_sort as i64,
                    cart_status: share_info_.card_status as i64,
                },
            );
            FBSDeviceShareCardInfoEntry::create(
                builder,
                &FBSDeviceShareCardInfoEntryArgs {
                    key: Some(key_),
                    value: Some(fbs_share_card_info),
                },
            )
        })
        .collect::<Vec<_>>();
    let card_info_map_vec = builder.create_vector(&card_info_map);
    let support_shared = product.shared_device_flag();
    let rebind = product.re_bind();
    let device_product = FBSDeviceProduct::create(
        builder,
        &FBSDeviceProductArgs {
            bar_code,
            brand: Some(brand),
            category: Some(category),
            category_code: Some(category_code),
            category_grouping: Some(category_grouping),
            two_grouping_name: Some(two_grouping_name),
            device_type,
            image_url: Some(image_url),
            bind_type,
            access_type,
            communication_mode,
            config_type: Some(config_type),
            app_type_icon: Some(app_type_icon),
            no_keep_alive: product.no_keep_alive(),
            card_page_icon: Some(card_page_icon),
            card_page_img: Some(card_page_img),
            small_card_sort: product.small_card_sort(),
            large_card_sort: product.large_card_sort(),
            card_sort: card_sort as i64,
            card_status: card_status as i64,
            aggregation_parent_id: Some(aggregation_parent_id),
            support_aggregation_flag: support_aggregation_flag as i64,
            shared_device_flag,
            card_info_map: Some(card_info_map_vec),
            support_shared,
            rebind: rebind as i64,
        },
    );
    let relation = device.get_relation();
    let owner_id = builder.create_string(&relation.owner_id());
    let owner_phone = builder.create_string(&relation.owner_phone());
    let family_id = builder.create_string(&relation.family_id());
    let uc_user_id = builder.create_string(&relation.uc_user_id());
    let floor_id = builder.create_string(&relation.floor_id());
    let floor_name = builder.create_string(&relation.floor_name());
    let floor_order_id = builder.create_string(&relation.floor_order_id());
    let device_relation = FBSDeviceRelation::create(
        builder,
        &FBSDeviceRelationArgs {
            owner_id: Some(owner_id),
            owner_phone: Some(owner_phone),
            family_id: Some(family_id),
            uc_user_id: Some(uc_user_id),
            floor_id: Some(floor_id),
            floor_name: Some(floor_name),
            floor_order_id: Some(floor_order_id),
        },
    );
    FBSDeviceInfo::create(
        builder,
        &FBSDeviceInfoArgs {
            device_base_info: Some(base_info),
            device_basic: Some(device_basic),
            device_permission: Some(device_permission),
            device_product: Some(device_product),
            device_relation: Some(device_relation),
        },
    )
}

fn create_fbs_device_attribute<'a>(
    builder: &mut FlatBufferBuilder<'a>,
    attribute: &UpDeviceAttribute,
) -> WIPOffset<FBSDeviceAttribute<'a>> {
    let name = builder.create_string(&attribute.name());
    let value = builder.create_string(&attribute.value());
    FBSDeviceAttribute::create(
        builder,
        &FBSDeviceAttributeArgs {
            name: Some(name),
            value: Some(value),
        },
    )
}

fn create_fbs_device_attribute_by_uhsd<'a>(
    builder: &mut FlatBufferBuilder<'a>,
    attribute: &UhsdDeviceAttribute,
) -> WIPOffset<FBSDeviceAttribute<'a>> {
    let name = builder.create_string(&attribute.name());
    let value = builder.create_string(&attribute.value());
    FBSDeviceAttribute::create(
        builder,
        &FBSDeviceAttributeArgs {
            name: Some(name),
            value: Some(value),
        },
    )
}

fn create_fbs_device_caution<'a>(
    builder: &mut FlatBufferBuilder<'a>,
    caution: &UpDeviceCaution,
) -> WIPOffset<FBSDeviceCaution<'a>> {
    let name = builder.create_string(&caution.name());
    let value = builder.create_string(&caution.value());
    let time = builder.create_string(&caution.time());
    FBSDeviceCaution::create(
        builder,
        &FBSDeviceCautionArgs {
            name: Some(name),
            value: Some(value),
            time: Some(time),
        },
    )
}

fn create_fbs_caution<'a>(
    builder: &mut FlatBufferBuilder<'a>,
    caution: &LECaution,
) -> WIPOffset<FBSCaution<'a>> {
    let name = builder.create_string(&caution.name());
    let code = builder.create_string(&caution.code());
    let desc = builder.create_string(&caution.desc());
    let time = builder.create_string(&caution.time());
    FBSCaution::create(
        builder,
        &FBSCautionArgs {
            name: Some(name),
            code: Some(code),
            desc: Some(desc),
            clear: caution.clear(),
            time: Some(time),
        },
    )
}

fn create_fbs_attribute<'a>(
    builder: &mut FlatBufferBuilder<'a>,
    attribute: &Arc<LEAttribute>,
) -> WIPOffset<FBSAttribute<'a>> {
    let name = builder.create_string(&attribute.name());
    let value = create_fbs_option_string(builder, &attribute.value());
    let code = attribute
        .codes()
        .iter()
        .map(|it| builder.create_string(it))
        .collect::<Vec<_>>();
    let fbs_code = builder.create_vector(&code);
    let desc = create_fbs_option_string(builder, &attribute.desc());
    let default_value = create_fbs_option_string(builder, &attribute.default_value());
    let operation_type = builder.create_string(&attribute.operation_type());
    let value_range: ValueRange = attribute.value_range().clone().into();
    let fbs_value_range = create_fbs_value_range(builder, &value_range);
    FBSAttribute::create(
        builder,
        &FBSAttributeArgs {
            name: Some(name),
            value,
            code: Some(fbs_code),
            desc,
            default_value,
            readable: attribute.readable(),
            writable: attribute.writable(),
            invisible: attribute.invisible(),
            value_range: Some(fbs_value_range),
            operation_type: Some(operation_type),
        },
    )
}

fn create_fbs_value_range<'a>(
    builder: &mut FlatBufferBuilder<'a>,
    range: &ValueRange,
) -> WIPOffset<FBSValueRange<'a>> {
    let data_list = range
        .data_list()
        .iter()
        .map(|it| create_fbs_data_item(builder, it))
        .collect::<Vec<_>>();
    let fbs_data_list = builder.create_vector(&data_list);
    let data_step = if let Some(data_step) = range.data_step() {
        Some(create_fbs_data_step(builder, &data_step))
    } else {
        None
    };
    let data_time = if let Some(data_time) = range.data_time() {
        Some(create_fbs_data_time(builder, &data_time))
    } else {
        None
    };
    let data_date = if let Some(data_date) = range.data_date() {
        Some(create_fbs_data_date(builder, &data_date))
    } else {
        None
    };
    let type_str = builder.create_string(&range.type_str());
    FBSValueRange::create(
        builder,
        &FBSValueRangeArgs {
            type_: Some(type_str),
            data_list: Some(fbs_data_list),
            data_step,
            data_time,
            data_date,
        },
    )
}

type ValueRangeEnum = logic_engine::device_config::value_range::ValueRange;

fn create_fbs_value_range_by_enum<'a>(
    builder: &mut FlatBufferBuilder<'a>,
    range: &ValueRangeEnum,
) -> WIPOffset<FBSValueRange<'a>> {
    let type_str = range.to_string();
    let fbs_type = builder.create_string(&type_str);
    let value_range_args = match range {
        ValueRangeEnum::List { data_list } => {
            let list_values = data_list
                .iter()
                .map(|it| create_fbs_data_item_by_enum(builder, it))
                .collect::<Vec<_>>();
            let fbs_data_list = builder.create_vector(&list_values);
            (Some(fbs_data_list), None, None, None)
        }
        ValueRangeEnum::Step { data_step } => {
            let fbs_data_step = create_fbs_data_step_by_enum(builder, data_step);
            (None, Some(fbs_data_step), None, None)
        }
        ValueRangeEnum::Time { data_time } => {
            let fbs_data_time = create_fbs_data_time_by_enum(builder, data_time);
            (None, None, Some(fbs_data_time), None)
        }
        ValueRangeEnum::Date { data_date } => {
            let fbs_data_date = create_fbs_data_date_by_enum(builder, data_date);
            (None, None, None, Some(fbs_data_date))
        }
        _ => (None, None, None, None),
    };
    FBSValueRange::create(
        builder,
        &FBSValueRangeArgs {
            type_: Some(fbs_type),
            data_list: value_range_args.0,
            data_step: value_range_args.1,
            data_time: value_range_args.2,
            data_date: value_range_args.3,
        },
    )
}

fn create_fbs_data_item<'a>(
    builder: &mut FlatBufferBuilder<'a>,
    data_item: &LEDataItem,
) -> WIPOffset<FBSDataItem<'a>> {
    let data = builder.create_string(&data_item.data());
    let code = create_fbs_option_string(builder, &data_item.code());
    let desc = create_fbs_option_string(builder, &data_item.desc());
    FBSDataItem::create(
        builder,
        &FBSDataItemArgs {
            data: Some(data),
            code,
            desc,
        },
    )
}

fn create_fbs_data_item_by_enum<'a>(
    builder: &mut FlatBufferBuilder<'a>,
    data_item: &ListValue,
) -> WIPOffset<FBSDataItem<'a>> {
    let data = builder.create_string(&data_item.data);
    let code = create_fbs_option_string(builder, &data_item.code);
    let desc = create_fbs_option_string(builder, &data_item.desc);
    FBSDataItem::create(
        builder,
        &FBSDataItemArgs {
            data: Some(data),
            code,
            desc,
        },
    )
}

fn create_fbs_data_step<'a>(
    builder: &mut FlatBufferBuilder<'a>,
    data_step: &DataStep,
) -> WIPOffset<FBSDataStep<'a>> {
    let data_type = builder.create_string(&data_step.data_type());
    let step = builder.create_string(&data_step.step());
    let min_value = builder.create_string(&data_step.min_value());
    let max_value = builder.create_string(&data_step.max_value());
    let fallback = create_fbs_option_string(builder, &data_step.fallback());
    let transform = if let Some(transform) = data_step.transform() {
        Some(create_fbs_transform(builder, transform))
    } else {
        None
    };
    FBSDataStep::create(
        builder,
        &FBSDataStepArgs {
            data_type: Some(data_type),
            step: Some(step),
            min_value: Some(min_value),
            max_value: Some(max_value),
            fallback,
            transform,
            unit: None, // TODO: 参数
        },
    )
}

fn create_fbs_data_step_by_enum<'a>(
    builder: &mut FlatBufferBuilder<'a>,
    data_step: &StepValue,
) -> WIPOffset<FBSDataStep<'a>> {
    let data_type = builder.create_string(&data_step.data_type);
    let step = builder.create_string(&data_step.step);
    let min_value = builder.create_string(&data_step.min_value);
    let max_value = builder.create_string(&data_step.max_value);
    let fallback = create_fbs_option_string(builder, &data_step.fallback);
    let transform = if let Some(transform) = &data_step.transform {
        Some(create_fbs_transform_by_enum(builder, transform))
    } else {
        None
    };
    FBSDataStep::create(
        builder,
        &FBSDataStepArgs {
            data_type: Some(data_type),
            step: Some(step),
            min_value: Some(min_value),
            max_value: Some(max_value),
            fallback,
            transform,
            unit: None, // TODO: 参数
        },
    )
}

fn create_fbs_transform<'a>(
    builder: &mut FlatBufferBuilder<'a>,
    transform: &LETransform,
) -> WIPOffset<FBSTransform<'a>> {
    let k = builder.create_string(&transform.k());
    let c = builder.create_string(&transform.c());
    FBSTransform::create(
        builder,
        &FBSTransformArgs {
            k: Some(k),
            c: Some(c),
        },
    )
}

fn create_fbs_transform_by_enum<'a>(
    builder: &mut FlatBufferBuilder<'a>,
    transform: &Transform,
) -> WIPOffset<FBSTransform<'a>> {
    let k = builder.create_string(&transform.k);
    let c = builder.create_string(&transform.c);
    FBSTransform::create(
        builder,
        &FBSTransformArgs {
            k: Some(k),
            c: Some(c),
        },
    )
}

fn create_fbs_data_time<'a>(
    builder: &mut FlatBufferBuilder<'a>,
    data_time: &LETimeValueRange,
) -> WIPOffset<FBSDataTime<'a>> {
    let format = builder.create_string(&data_time.format());
    FBSDataTime::create(
        builder,
        &FBSDataTimeArgs {
            format: Some(format),
            min_hour: data_time.min_hour(),
            max_hour: data_time.max_hour(),
            min_minute: data_time.min_minute(),
            max_minute: data_time.max_minute(),
            min_second: data_time.min_second(),
            max_second: data_time.max_second(),
        },
    )
}

fn create_fbs_data_time_by_enum<'a>(
    builder: &mut FlatBufferBuilder<'a>,
    data_time: &TimeValue,
) -> WIPOffset<FBSDataTime<'a>> {
    let format = builder.create_string(&data_time.format);
    FBSDataTime::create(
        builder,
        &FBSDataTimeArgs {
            format: Some(format),
            min_hour: data_time.min_hour,
            max_hour: data_time.max_hour,
            min_minute: data_time.min_minute,
            max_minute: data_time.max_minute,
            min_second: data_time.min_second,
            max_second: data_time.max_second,
        },
    )
}

fn create_fbs_data_date<'a>(
    builder: &mut FlatBufferBuilder<'a>,
    date: &LEDateValueRange,
) -> WIPOffset<FBSDataDate<'a>> {
    let format = builder.create_string(&date.format());
    let begin_date = builder.create_string(&date.begin_date());
    let end_date = builder.create_string(&date.end_date());
    FBSDataDate::create(
        builder,
        &FBSDataDateArgs {
            format: Some(format),
            begin_date: Some(begin_date),
            end_date: Some(end_date),
        },
    )
}

fn create_fbs_data_date_by_enum<'a>(
    builder: &mut FlatBufferBuilder<'a>,
    date: &DateValue,
) -> WIPOffset<FBSDataDate<'a>> {
    let format = builder.create_string(&date.format);
    let begin_date = builder.create_string(&date.begin_date);
    let end_date = builder.create_string(&date.end_date);
    FBSDataDate::create(
        builder,
        &FBSDataDateArgs {
            format: Some(format),
            begin_date: Some(begin_date),
            end_date: Some(end_date),
        },
    )
}

fn create_fbs_modifier<'a>(
    builder: &mut FlatBufferBuilder<'a>,
    modifier: &Modifier,
) -> WIPOffset<FBSModifier<'a>> {
    let priority = if let Some(temp_priority) = modifier.priority {
        temp_priority
    } else {
        0
    };
    let trigger = create_fbs_trigger(builder, &modifier.trigger);
    let actions = modifier
        .actions
        .iter()
        .map(|it| create_fbs_action(builder, it))
        .collect::<Vec<_>>();
    let fbs_actions = builder.create_vector(&actions);
    FBSModifier::create(
        builder,
        &FBSModifierArgs {
            priority,
            trigger: Some(trigger),
            actions: Some(fbs_actions),
        },
    )
}

fn create_fbs_trigger<'a>(
    builder: &mut FlatBufferBuilder<'a>,
    trigger: &ModifierTrigger,
) -> WIPOffset<FBSTrigger<'a>> {
    let operator = builder.create_string(&trigger.operator);
    let fbs_conditions = trigger.conditions.as_ref().map_or(vec![], |it| {
        it.iter()
            .map(|condition| create_fbs_str_arr_entry(builder, &condition.0, &condition.1))
            .collect::<Vec<_>>()
    });
    let fbs_conditions_vec = builder.create_vector(&fbs_conditions);
    let alarms = if let Some(temp_alarms) = trigger.alarms.clone() {
        let new_alarms = temp_alarms
            .iter()
            .map(|it| builder.create_string(it))
            .collect::<Vec<_>>();
        Some(builder.create_vector(&new_alarms))
    } else {
        None
    };
    FBSTrigger::create(
        builder,
        &FBSTriggerArgs {
            operator: Some(operator),
            conditions: Some(fbs_conditions_vec),
            alarms,
        },
    )
}

fn create_fbs_str_arr_entry<'a>(
    builder: &mut FlatBufferBuilder<'a>,
    key: &str,
    values: &Vec<String>,
) -> WIPOffset<StringArrayEntryWrapper<'a>> {
    let fbs_key = builder.create_string(key);
    let fbs_values = values
        .iter()
        .map(|it| builder.create_string(it))
        .collect::<Vec<_>>();
    let fbs_values_vec = builder.create_vector(&fbs_values);
    StringArrayEntryWrapper::create(
        builder,
        &StringArrayEntryWrapperArgs {
            key: Some(fbs_key),
            values: Some(fbs_values_vec),
        },
    )
}

fn create_fbs_action<'a>(
    builder: &mut FlatBufferBuilder<'a>,
    action: &ModifierAction,
) -> WIPOffset<FBSAction<'a>> {
    let name = builder.create_string(&action.name);
    let rewrite_fields = builder.create_string(&action.rewrite_fields);
    let default_value = create_fbs_option_string(builder, &action.default_value);
    let fbs_value_range = action
        .value_range
        .as_ref()
        .map(|it| create_fbs_value_range_by_enum(builder, it));
    FBSAction::create(
        builder,
        &FBSActionArgs {
            name: Some(name),
            rewrite_fields: Some(rewrite_fields),
            default_value,
            writable: action.writable.unwrap_or(false),
            value_range: fbs_value_range,
        },
    )
}

fn create_fbs_business_attr<'a>(
    builder: &mut FlatBufferBuilder<'a>,
    business_attr: &BusinessAttr,
) -> WIPOffset<FBSBusinessAttr<'a>> {
    let name = builder.create_string(&business_attr.name);
    let desc = builder.create_string(&business_attr.desc);
    let desc1 = create_fbs_option_string(builder, &business_attr.desc1);
    let desc2 = create_fbs_option_string(builder, &business_attr.desc1);
    let desc3 = create_fbs_option_string(builder, &business_attr.desc1);
    let desc4 = create_fbs_option_string(builder, &business_attr.desc1);
    let detail_desc = create_fbs_option_string(builder, &business_attr.desc1);
    let alias = create_fbs_option_string(builder, &business_attr.desc1);
    let cmd_para = create_fbs_cmd_para(builder, &business_attr.cmd_para);
    FBSBusinessAttr::create(
        builder,
        &FBSBusinessAttrArgs {
            name: Some(name),
            desc: Some(desc),
            desc1,
            desc2,
            desc3,
            desc4,
            detail_desc,
            alias,
            cmd_para: Some(cmd_para),
        },
    )
}

fn create_fbs_cmd_para<'a>(
    builder: &mut FlatBufferBuilder<'a>,
    cmd_para: &CmdPara,
) -> WIPOffset<FBSCmdPara<'a>> {
    let name = builder.create_string(&cmd_para.name);
    let attr_name_list = cmd_para
        .attr_name_list
        .iter()
        .map(|it| create_fbs_cmd_para_attr(builder, it))
        .collect::<Vec<_>>();
    let fbs_attr_name_list = builder.create_vector(&attr_name_list);
    FBSCmdPara::create(
        builder,
        &FBSCmdParaArgs {
            name: Some(name),
            attr_name_list: Some(fbs_attr_name_list),
        },
    )
}

fn create_fbs_cmd_para_attr<'a>(
    builder: &mut FlatBufferBuilder<'a>,
    cmd_para_attr: &CmdParaAttr,
) -> WIPOffset<FBSCmdParaAttr<'a>> {
    let name = builder.create_string(&cmd_para_attr.name);
    let value_type = builder.create_string(&cmd_para_attr.value_type);
    let default_value = create_fbs_option_string(builder, &cmd_para_attr.default_value);
    let fbs_value_range = cmd_para_attr
        .value_range
        .as_ref()
        .map(|it| create_fbs_value_range_by_enum(builder, it));
    FBSCmdParaAttr::create(
        builder,
        &FBSCmdParaAttrArgs {
            name: Some(name),
            value_type: Some(value_type),
            default_value,
            value_range: fbs_value_range,
        },
    )
}

fn create_fbs_device_feedback<'a>(
    builder: &mut FlatBufferBuilder<'a>,
    device_feedback: &DeviceFeedback,
) -> WIPOffset<FBSDeviceFeedback<'a>> {
    let device_id = builder.create_string(&device_feedback.device_id);
    let req_sn = device_feedback.req_sn;
    let error_info = create_fbs_error_info(builder, &device_feedback.error_info);
    FBSDeviceFeedback::create(
        builder,
        &FBSDeviceFeedbackArgs {
            device_id: Some(device_id),
            req_sn,
            error_info: Some(error_info),
        },
    )
}

fn create_fbs_error_info<'a>(
    builder: &mut FlatBufferBuilder<'a>,
    error_info: &ErrorInfo,
) -> WIPOffset<FBSErrorInfo<'a>> {
    let code = error_info.code;
    let desc = builder.create_string(&error_info.desc);
    let pair_list = &error_info
        .pair_list
        .iter()
        .map(|it| {
            let name = builder.create_string(&it.name);
            let value = builder.create_string(&it.value);
            FBSUhsdPair::create(
                builder,
                &FBSUhsdPairArgs {
                    name: Some(name),
                    value: Some(value),
                },
            )
        })
        .collect::<Vec<_>>();
    let fbs_pair_list = builder.create_vector(pair_list);
    FBSErrorInfo::create(
        builder,
        &FBSErrorInfoArgs {
            code,
            desc: Some(desc),
            pair_list: Some(fbs_pair_list),
        },
    )
}

fn create_fbs_none<'a>(builder: &mut FlatBufferBuilder<'a>) -> WIPOffset<NoneWrapper<'a>> {
    NoneWrapper::create(builder, &NoneWrapperArgs {})
}

// result

fn void_result() -> Vec<u8> {
    let mut builder = FlatBufferBuilder::with_capacity(DEFAULT_SIZE);
    let none_wrapper = create_fbs_none(&mut builder);
    let code = builder.create_string(SUCCESS_CODE);
    let error = builder.create_string(SUCCESS);
    let flat = UpDeviceFlat::create(
        &mut builder,
        &UpDeviceFlatArgs {
            container_type: UpDeviceContainer::NoneWrapper,
            container: Some(none_wrapper.as_union_value()),
            code: Some(code),
            error: Some(error),
        },
    );
    builder.finish(flat, None);
    builder.finished_data().to_vec()
}

fn data_result(
    builder: &mut FlatBufferBuilder,
    data: WIPOffset<UnionWIPOffset>,
    container_type: UpDeviceContainer,
) -> Vec<u8> {
    let code = builder.create_string(SUCCESS_CODE);
    let error = builder.create_string(SUCCESS);
    let device_result = UpDeviceFlat::create(
        builder,
        &UpDeviceFlatArgs {
            container_type,
            container: Some(data),
            code: Some(code),
            error: Some(error),
        },
    );
    builder.finish(device_result, None);
    builder.finished_data().to_vec()
}

fn string_result(result: String) -> Vec<u8> {
    let mut builder = FlatBufferBuilder::with_capacity(DEFAULT_SIZE);
    let value = builder.create_string(&result);
    let string_result = StrWrapper::create(&mut builder, &StrWrapperArgs { value: Some(value) });
    data_result(
        &mut builder,
        string_result.as_union_value(),
        UpDeviceContainer::StrWrapper,
    )
}

fn bool_result(value: bool) -> Vec<u8> {
    let mut builder = FlatBufferBuilder::with_capacity(DEFAULT_SIZE);
    let bool_result = BoolWrapper::create(&mut builder, &BoolWrapperArgs { value });
    data_result(
        &mut builder,
        bool_result.as_union_value(),
        UpDeviceContainer::BoolWrapper,
    )
}

fn invalid_arg_result(error_message: &str) -> Vec<u8> {
    failure_result(error_message, PARAMS_ERROR)
}

fn no_device_result(device_id: &str) -> Vec<u8> {
    failure_result(&format!("device not found: {}", device_id), NO_DEVICE_ERROR)
}

fn no_attr_result(device_id: &str, attr_name: &str) -> Vec<u8> {
    failure_result(
        &format!("attribute not found: {}, {}", device_id, attr_name),
        NO_ATTRIBUTE_ERROR,
    )
}

fn failure_result(error_message: &str, error_code: &str) -> Vec<u8> {
    error!(
        "updevice ffi errormessage:{},error_code:{}",
        error_message, error_code
    );
    let mut builder = FlatBufferBuilder::with_capacity(DEFAULT_SIZE);
    let error_message = builder.create_string(error_message);
    let error_code = builder.create_string(error_code);
    let none_wrapper = create_fbs_none(&mut builder);
    let updevice_flat = UpDeviceFlat::create(
        &mut builder,
        &UpDeviceFlatArgs {
            container_type: UpDeviceContainer::NoneWrapper,
            container: Some(none_wrapper.as_union_value()),
            code: Some(error_code),
            error: Some(error_message),
        },
    );
    builder.finish(updevice_flat, None);
    builder.finished_data().to_vec()
}
