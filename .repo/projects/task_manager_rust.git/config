[core]
	repositoryformatversion = 0
	filemode = true
	precomposeunicode = true
[filter "lfs"]
	smudge = git-lfs smudge --skip -- %f
	process = git-lfs filter-process --skip
[remote "origin"]
	url = ssh://*****************:18022/uplus/rust/task_manager_rust
	projectname = uplus/rust/task_manager_rust
	fetch = +refs/heads/*:refs/remotes/origin/*
[branch "release_0.2.0"]
	remote = origin
	merge = refs/heads/release_0.2.0
	vscode-merge-base = origin/release_0.2.0
	vscode-merge-base = origin/release_0.2.0
