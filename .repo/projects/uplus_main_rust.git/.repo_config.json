{"core.repositoryformatversion": ["0"], "core.filemode": ["true"], "core.precomposeunicode": ["true"], "filter.lfs.smudge": ["git-lfs smudge --skip -- %f"], "filter.lfs.process": ["git-lfs filter-process --skip"], "remote.origin.url": ["ssh://*****************:18022/uplus/rust/uplus_main_rust"], "remote.origin.projectname": ["uplus/rust/uplus_main_rust"], "remote.origin.fetch": ["+refs/heads/*:refs/remotes/origin/*"], "branch.task_SYN-T280058.remote": ["origin"], "branch.task_SYN-T280058.merge": ["refs/heads/task_SYN-T280058"]}