[core]
	repositoryformatversion = 0
	filemode = true
	precomposeunicode = true
[filter "lfs"]
	smudge = git-lfs smudge --skip -- %f
	process = git-lfs filter-process --skip
[remote "origin"]
	url = ssh://*****************:18022/uplus/rust/uplus_main_rust
	projectname = uplus/rust/uplus_main_rust
	fetch = +refs/heads/*:refs/remotes/origin/*
[branch "task_SYN-T280058"]
	remote = origin
	merge = refs/heads/task_SYN-T280058
[branch "feature_SYN-R9552"]
	remote = origin
	merge = refs/heads/feature_SYN-R9552
[branch "release_1.1.0"]
	remote = origin
	merge = refs/heads/release_1.1.0
	vscode-merge-base = origin/release_1.1.0
