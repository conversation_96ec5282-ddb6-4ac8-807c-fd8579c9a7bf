0000000000000000000000000000000000000000 cc27e94237cbc27d370a7bee4aff2ec2fcc8bd95 yangliuqing <<EMAIL>> 1747621211 +0800	branch: Created from refs/remotes/origin/task_SYN-T281516
cc27e94237cbc27d370a7bee4aff2ec2fcc8bd95 44d15ef66171edcda964c198df20077504f3bc5f yangliuqing <<EMAIL>> 1747633952 +0800	commit: rust ohos channel example
44d15ef66171edcda964c198df20077504f3bc5f 44451e59bd91770ec607e4fe4b86219813cc5804 yangliuqing <<EMAIL>> 1747634157 +0800	commit (amend): rust ohos channel example
44451e59bd91770ec607e4fe4b86219813cc5804 a97a25ccc1010237649119d0ede1aa1c36da8176 yangliuqing <<EMAIL>> 1747635790 +0800	commit (amend): rust ohos channel example
a97a25ccc1010237649119d0ede1aa1c36da8176 b1400647b52c73aa6baa7d6e8a233ad7b7411b48 yangliuqing <<EMAIL>> 1747705179 +0800	commit (amend): rust ohos channel example
b1400647b52c73aa6baa7d6e8a233ad7b7411b48 52ac6a6a56c30778a41a808edffc97cc2d150e55 yangliuqing <<EMAIL>> 1747706144 +0800	commit (amend): rust ohos channel example
52ac6a6a56c30778a41a808edffc97cc2d150e55 7229e7e4798883baa48229bce633796063b26aed yangliuqing <<EMAIL>> 1748250525 +0800	pull: Fast-forward
7229e7e4798883baa48229bce633796063b26aed 0f123684b0ffcdfb612dc4d1ddf479306831fc89 yangliuqing <<EMAIL>> 1748250538 +0800	merge origin/release_0.5.0: Fast-forward
0f123684b0ffcdfb612dc4d1ddf479306831fc89 23b9d04d0fb74cb58659427e469f2d891dafafee yangliuqing <<EMAIL>> 1748250564 +0800	commit: fix feature-rust
23b9d04d0fb74cb58659427e469f2d891dafafee a2d69db21da0c1abd9706584e7223073d5b25418 yangliuqing <<EMAIL>> 1748255150 +0800	commit (amend): fix feature-rust
a2d69db21da0c1abd9706584e7223073d5b25418 a9471574ecfd2cd1e37bafe8422fbfbe45155672 yangliuqing <<EMAIL>> 1748255464 +0800	commit (amend): fix feature-rust
a9471574ecfd2cd1e37bafe8422fbfbe45155672 4ed172c31bebf999a1911fff630592b575f5e82f yangliuqing <<EMAIL>> 1748255712 +0800	commit (amend): fix feature-rust
4ed172c31bebf999a1911fff630592b575f5e82f caacf378c8247b4c1fd454d4680f2269d115a61c yangliuqing <<EMAIL>> 1748916810 +0800	pull: Fast-forward
caacf378c8247b4c1fd454d4680f2269d115a61c 775d361353272b385001a7143fc684b3bd1263ef yangliuqing <<EMAIL>> 1748937941 +0800	merge origin/feature_SYN-R9552: Fast-forward
775d361353272b385001a7143fc684b3bd1263ef 832873697e92bb83fb6266366beed3bb83555fd4 yangliuqing <<EMAIL>> 1749638310 +0800	commit: ohos updevice 适配Flatbuffer接口
832873697e92bb83fb6266366beed3bb83555fd4 d25daa53e86ecca8a785b91dc633e2ab59f0f3cc yangliuqing <<EMAIL>> 1749642785 +0800	commit (merge): Merge remote-tracking branch 'origin/feature_SYN-R9552' into task_SYN-T281516
d25daa53e86ecca8a785b91dc633e2ab59f0f3cc 9bd8f1cde97674eaaf7d4c619f033fd52be34b16 yangliuqing <<EMAIL>> 1749643749 +0800	commit (amend): Merge remote-tracking branch 'origin/feature_SYN-R9552' into task_SYN-T281516
9bd8f1cde97674eaaf7d4c619f033fd52be34b16 70016241864e1957d518a50ed0fb18011d425990 yangliuqing <<EMAIL>> 1749689445 +0800	commit: update usdk_rust tag
70016241864e1957d518a50ed0fb18011d425990 6f9c69df946fbe17918f5a858e49de2eedc9d186 yangliuqing <<EMAIL>> 1749693229 +0800	commit (amend): update usdk_rust tag
6f9c69df946fbe17918f5a858e49de2eedc9d186 1c1ec8f4e131b0b958c54e6d029be54fd8cdd155 yangliuqing <<EMAIL>> 1749697443 +0800	commit (amend): update usdk_rust tag
1c1ec8f4e131b0b958c54e6d029be54fd8cdd155 929259c1689c893eb240450a094b3a2d87f3b9c9 yangliuqing <<EMAIL>> 1749697553 +0800	commit (amend): update usdk_rust tag; fix bug
929259c1689c893eb240450a094b3a2d87f3b9c9 11d11f99dc784a66567b73d8526f4e9364725d11 yangliuqing <<EMAIL>> 1749781146 +0800	commit: fix uhsd pair
11d11f99dc784a66567b73d8526f4e9364725d11 92ce106ea660f65515067e0891fb947c14128a4c yangliuqing <<EMAIL>> 1749781150 +0800	pull -r (finish): refs/heads/task_SYN-T281516 onto e675e2690ed90f258de2ce044594a3540ed6d2f5
92ce106ea660f65515067e0891fb947c14128a4c 9b54b7e56a9cba33ebef4d1001dca8ac682a726c yangliuqing <<EMAIL>> 1749802037 +0800	commit: optimize init param
9b54b7e56a9cba33ebef4d1001dca8ac682a726c 852d69d35b0d2698846ea2b9b683b12647f068d7 yangliuqing <<EMAIL>> 1749802048 +0800	pull -r (finish): refs/heads/task_SYN-T281516 onto 0e6eb85747029e4d608a212cd09d3c91f9fc30ef
