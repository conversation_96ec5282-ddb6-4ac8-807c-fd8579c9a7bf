[core]
	repositoryformatversion = 0
	filemode = true
	precomposeunicode = true
[filter "lfs"]
	smudge = git-lfs smudge --skip -- %f
	process = git-lfs filter-process --skip
[remote "origin"]
	url = ssh://*****************:18022/uplus/rust/updevice_rust
	projectname = uplus/rust/updevice_rust
	fetch = +refs/heads/*:refs/remotes/origin/*
[branch "task_SYN-T281516"]
	remote = origin
	merge = refs/heads/task_SYN-T281516
[branch "release_0.5.0"]
	remote = origin
	merge = refs/heads/release_0.5.0
	vscode-merge-base = origin/release_0.5.0
[branch "task_SYN-T286932"]
	remote = origin
	merge = refs/heads/task_SYN-T286932
[branch "task_SYN-T287221"]
	remote = origin
	merge = refs/heads/task_SYN-T287221
