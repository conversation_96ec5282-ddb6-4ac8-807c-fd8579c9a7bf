{"core.repositoryformatversion": ["0"], "core.filemode": ["true"], "core.precomposeunicode": ["true"], "filter.lfs.smudge": ["git-lfs smudge --skip -- %f"], "filter.lfs.process": ["git-lfs filter-process --skip"], "remote.origin.url": ["ssh://*****************:18022/uplus/rust/updevice_rust"], "remote.origin.projectname": ["uplus/rust/updevice_rust"], "remote.origin.fetch": ["+refs/heads/*:refs/remotes/origin/*"], "branch.task_SYN-T281516.remote": ["origin"], "branch.task_SYN-T281516.merge": ["refs/heads/task_SYN-T281516"], "branch.release_0.5.0.remote": ["origin"], "branch.release_0.5.0.merge": ["refs/heads/release_0.5.0"], "branch.task_SYN-T286932.remote": ["origin"], "branch.task_SYN-T286932.merge": ["refs/heads/task_SYN-T286932"], "branch.task_SYN-T287221.remote": ["origin"], "branch.task_SYN-T287221.merge": ["refs/heads/task_SYN-T287221"]}