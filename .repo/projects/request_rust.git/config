[core]
	repositoryformatversion = 0
	filemode = true
	precomposeunicode = true
[filter "lfs"]
	smudge = git-lfs smudge --skip -- %f
	process = git-lfs filter-process --skip
[remote "origin"]
	url = ssh://*****************:18022/uplus/rust/request_rust
	projectname = uplus/rust/request_rust
	fetch = +refs/heads/*:refs/remotes/origin/*
[branch "task_SYN-T299148"]
	remote = origin
	merge = refs/heads/task_SYN-T299148
	vscode-merge-base = origin/task_SYN-T299148
	vscode-merge-base = origin/task_SYN-T299148
