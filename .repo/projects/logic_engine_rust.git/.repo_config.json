{"core.repositoryformatversion": ["0"], "core.filemode": ["true"], "core.precomposeunicode": ["true"], "filter.lfs.smudge": ["git-lfs smudge --skip -- %f"], "filter.lfs.process": ["git-lfs filter-process --skip"], "remote.origin.url": ["ssh://*****************:18022/uplus/rust/logic_engine_rust"], "remote.origin.projectname": ["uplus/rust/logic_engine_rust"], "remote.origin.fetch": ["+refs/heads/*:refs/remotes/origin/*"], "branch.feature_SYN-R9552.remote": ["origin"], "branch.feature_SYN-R9552.merge": ["refs/heads/feature_SYN-R9552"]}