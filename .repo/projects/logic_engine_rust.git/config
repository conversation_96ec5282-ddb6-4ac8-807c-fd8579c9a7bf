[core]
	repositoryformatversion = 0
	filemode = true
	precomposeunicode = true
[filter "lfs"]
	smudge = git-lfs smudge --skip -- %f
	process = git-lfs filter-process --skip
[remote "origin"]
	url = ssh://*****************:18022/uplus/rust/logic_engine_rust
	projectname = uplus/rust/logic_engine_rust
	fetch = +refs/heads/*:refs/remotes/origin/*
[branch "feature_SYN-R9552"]
	remote = origin
	merge = refs/heads/feature_SYN-R9552
[branch "task_SYN-T296198"]
	remote = origin
	merge = refs/heads/task_SYN-T296198
[branch "task_SYN-T299198"]
	remote = origin
	merge = refs/heads/task_SYN-T299198
[branch "release_0.5.0"]
	remote = origin
	merge = refs/heads/release_0.5.0
	vscode-merge-base = origin/release_0.5.0
