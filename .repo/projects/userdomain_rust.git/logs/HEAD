ba7b65dd1d4e08d9b8fc9e8b05ee5f094da6079d c807a3af20f40671b3b1cc74822e421bd0de849f yang<PERSON><PERSON><PERSON> <<EMAIL>> 1747374436 +0800	checkout: moving from ba7b65dd1d4e08d9b8fc9e8b05ee5f094da6079d to release_0.4.0
c807a3af20f40671b3b1cc74822e421bd0de849f 863479a563d9a140abb3883cb0d6f7ae2271e756 yangliuqing <<EMAIL>> 1747375593 +0800	checkout: moving from release_0.4.0 to task_SYN-T280815
863479a563d9a140abb3883cb0d6f7ae2271e756 57b2672e9ac0ee116eb92ade1bd6fe0524b6eea5 yangliuqing <yang<PERSON><EMAIL>> 1747377255 +0800	commit: gen ts fbs bean files
57b2672e9ac0ee116eb92ade1bd6fe0524b6eea5 9805f73c0f240a5e36ac80bc5cb6a97aa3c53ce0 yangliuqing <<EMAIL>> 1747378556 +0800	commit: arkts fbs bean files
9805f73c0f240a5e36ac80bc5cb6a97aa3c53ce0 cf2828a437a78c9c99b453d099258d6c50397aa4 yangliuqing <<EMAIL>> 1747379178 +0800	pull: Fast-forward
cf2828a437a78c9c99b453d099258d6c50397aa4 b42642d48ecbe235a8a065134bdd9016c3dc3c52 yangliuqing <<EMAIL>> 1747704632 +0800	commit: arkts flatbuffer for userdomain
b42642d48ecbe235a8a065134bdd9016c3dc3c52 d9f433278fefb9c6129e6f6a68c127ef47eae6d6 yangliuqing <<EMAIL>> 1747704662 +0800	merge origin/feature_SYN-R9552: Merge made by the 'ort' strategy.
d9f433278fefb9c6129e6f6a68c127ef47eae6d6 77e1cebe1e706f93b0dd85ac4c85f10f2658325f yangliuqing <<EMAIL>> 1747708977 +0800	commit: fbs modle write
77e1cebe1e706f93b0dd85ac4c85f10f2658325f dd68bc35e031f1408ae799fcfb7971a45e2879d7 yangliuqing <<EMAIL>> 1747709002 +0800	rebase (start): checkout origin/release_0.4.0
dd68bc35e031f1408ae799fcfb7971a45e2879d7 4cb09d25e4e2e8c69e60e3030e63abce17fda757 yangliuqing <<EMAIL>> 1747709002 +0800	rebase (pick): arkts flatbuffer for userdomain
4cb09d25e4e2e8c69e60e3030e63abce17fda757 749887454e46ecb99e85c244d82466454775c4fd yangliuqing <<EMAIL>> 1747709002 +0800	rebase (pick): fbs modle write
749887454e46ecb99e85c244d82466454775c4fd 749887454e46ecb99e85c244d82466454775c4fd yangliuqing <<EMAIL>> 1747709002 +0800	rebase (finish): returning to refs/heads/task_SYN-T280815
749887454e46ecb99e85c244d82466454775c4fd ee5b6d5c3699d85d0315ef840f7a0ee0f7880218 yangliuqing <<EMAIL>> 1747731249 +0800	commit (amend): fbs modle write
ee5b6d5c3699d85d0315ef840f7a0ee0f7880218 b282790c6bcf0ab46ee8126e3d711eec7123a5a0 yangliuqing <<EMAIL>> 1747734657 +0800	pull: Fast-forward
b282790c6bcf0ab46ee8126e3d711eec7123a5a0 314fb684da376bf088d6636f3c402c9501d5bf4a yangliuqing <<EMAIL>> 1747989023 +0800	commit: arkts flatbuffer
314fb684da376bf088d6636f3c402c9501d5bf4a 8297e8cafc83ff987244f4012d41d97ea64ee7bd yangliuqing <<EMAIL>> 1747994779 +0800	commit (merge): Merge remote-tracking branch 'origin/feature_SYN-R9552' into task_SYN-T280815
8297e8cafc83ff987244f4012d41d97ea64ee7bd 366c9b24c16e71e4c61e94d5f293eed19d37d0aa yangliuqing <<EMAIL>> 1747997793 +0800	commit (amend): Merge remote-tracking branch 'origin/feature_SYN-R9552' into task_SYN-T280815
366c9b24c16e71e4c61e94d5f293eed19d37d0aa 594100f438a97abdea79fa531d0a6ecbd8a28e7a yangliuqing <<EMAIL>> 1748324391 +0800	commit: test flat api
594100f438a97abdea79fa531d0a6ecbd8a28e7a 785aae87b41d35f33fcb796d2dc11fc01110661b yangliuqing <<EMAIL>> 1748324416 +0800	rebase (start): checkout origin/feature_SYN-R9552
785aae87b41d35f33fcb796d2dc11fc01110661b 594100f438a97abdea79fa531d0a6ecbd8a28e7a yangliuqing <<EMAIL>> 1748324483 +0800	rebase (abort): returning to refs/heads/task_SYN-T280815
594100f438a97abdea79fa531d0a6ecbd8a28e7a fa6d30d49ccae60c5377dd73b32622bb386baf68 yangliuqing <<EMAIL>> 1748327492 +0800	commit (merge): Merge remote-tracking branch 'origin/feature_SYN-R9552' into task_SYN-T280815
fa6d30d49ccae60c5377dd73b32622bb386baf68 d8ce404ed35f25bcfe96073448c5e511c3792061 yangliuqing <<EMAIL>> 1748328804 +0800	commit: update fbs ts file
d8ce404ed35f25bcfe96073448c5e511c3792061 5cbe71f39c974c9628e5c6e94339e8765093d929 yangliuqing <<EMAIL>> 1748329368 +0800	commit (amend): update fbs ts file
5cbe71f39c974c9628e5c6e94339e8765093d929 4d17dcf4933b6332015d88e3a2d12210d152d497 yangliuqing <<EMAIL>> 1748329987 +0800	commit (amend): update fbs ts file
4d17dcf4933b6332015d88e3a2d12210d152d497 50794c3945d8b644e5471397bca7165bacb958a5 yangliuqing <<EMAIL>> 1748395264 +0800	pull: Fast-forward
50794c3945d8b644e5471397bca7165bacb958a5 2d950d2dac3c7d055748387044fbd5362de8da60 yangliuqing <<EMAIL>> 1748509087 +0800	commit: 调试接口 & 修复接口错误
2d950d2dac3c7d055748387044fbd5362de8da60 10fa46839c9aae8a1407ef113b6e4017057e653f yangliuqing <<EMAIL>> 1748571665 +0800	commit: 完成接口调试
10fa46839c9aae8a1407ef113b6e4017057e653f c807a3af20f40671b3b1cc74822e421bd0de849f yangliuqing <<EMAIL>> 1748916464 +0800	checkout: moving from task_SYN-T280815 to release_0.4.0
c807a3af20f40671b3b1cc74822e421bd0de849f 4899d14d10b963bc235712366b5975db13f41b71 yangliuqing <<EMAIL>> 1748916466 +0800	pull: Fast-forward
4899d14d10b963bc235712366b5975db13f41b71 10fa46839c9aae8a1407ef113b6e4017057e653f yangliuqing <<EMAIL>> 1749695364 +0800	checkout: moving from release_0.4.0 to task_SYN-T280815
10fa46839c9aae8a1407ef113b6e4017057e653f 9cc5af35e2036dff548df619be3862f2b7abb360 yangliuqing <<EMAIL>> 1749695425 +0800	commit: fix bug
9cc5af35e2036dff548df619be3862f2b7abb360 7c3af1b235a5cc42f4b70264beea0c873eb1eec3 yangliuqing <<EMAIL>> 1749695453 +0800	pull -r (start): checkout 7c3af1b235a5cc42f4b70264beea0c873eb1eec3
7c3af1b235a5cc42f4b70264beea0c873eb1eec3 60429e5db23d91d4e602a4c4a29d3a9438b77dcb yangliuqing <<EMAIL>> 1749695453 +0800	pull -r (pick): fix bug
60429e5db23d91d4e602a4c4a29d3a9438b77dcb 60429e5db23d91d4e602a4c4a29d3a9438b77dcb yangliuqing <<EMAIL>> 1749695453 +0800	pull -r (finish): returning to refs/heads/task_SYN-T280815
60429e5db23d91d4e602a4c4a29d3a9438b77dcb 61cff2e82ee73e176cbc6248e79284befd7d37af yangliuqing <<EMAIL>> 1749696586 +0800	commit (amend): fix bug
61cff2e82ee73e176cbc6248e79284befd7d37af 49817147f224be47cb0c11ccab721f187a94070b yangliuqing <<EMAIL>> 1749697109 +0800	commit (amend): fix bug
49817147f224be47cb0c11ccab721f187a94070b dde5157f12fa14d3e000976907fe77b32eeae97b yangliuqing <<EMAIL>> 1749697198 +0800	commit (amend): fix bug
dde5157f12fa14d3e000976907fe77b32eeae97b bf567b3c4270914f621912768048eb3800ead39b yangliuqing <<EMAIL>> 1749697641 +0800	commit (amend): fix bug
bf567b3c4270914f621912768048eb3800ead39b 7a162619dc9fb669b723e248aa941c69b348162f yangliuqing <<EMAIL>> 1750125810 +0800	checkout: moving from task_SYN-T280815 to task_SYN-T298906
7a162619dc9fb669b723e248aa941c69b348162f 5448d3e6892dbf125c69fb1c150c6fcd1fbefeb7 yangliuqing <<EMAIL>> 1750125858 +0800	commit: 联调bug修复: Invalid type: null, expected struct MemberInfo
5448d3e6892dbf125c69fb1c150c6fcd1fbefeb7 2da7eec1e18919b5552118101a128c84e99a7cbc yangliuqing <<EMAIL>> 1750138940 +0800	pull: Fast-forward
2da7eec1e18919b5552118101a128c84e99a7cbc 1bfc58d1013907f0834cd1ebac16c28f85845410 yangliuqing <<EMAIL>> 1750138954 +0800	commit: fix memberinfo error
1bfc58d1013907f0834cd1ebac16c28f85845410 4dea356a94348bc0d916c1c0d573c99cb4613f48 yangliuqing <<EMAIL>> 1750140679 +0800	commit (amend): fix memberinfo error
4dea356a94348bc0d916c1c0d573c99cb4613f48 03d739ddd728de755178ccf3ac2015227e18d093 yangliuqing <<EMAIL>> 1750141072 +0800	commit (amend): fix memberinfo error
03d739ddd728de755178ccf3ac2015227e18d093 4def2f3dc9a6a96dd05b854200421a6b9d55ad8d yangliuqing <<EMAIL>> 1750142151 +0800	commit (amend): fix memberinfo error
4def2f3dc9a6a96dd05b854200421a6b9d55ad8d c7756b76592bb1e7354892b7f0e0b6c9b06e2cac yangliuqing <<EMAIL>> 1750237962 +0800	pull: Fast-forward
c7756b76592bb1e7354892b7f0e0b6c9b06e2cac b001320a4079d4097c90b4d821cad31d1ba670a8 yangliuqing <<EMAIL>> 1750237974 +0800	commit: fix args error
b001320a4079d4097c90b4d821cad31d1ba670a8 95ddc525744e584f2dd22147b69cfd10dad3fb45 yangliuqing <<EMAIL>> 1750314287 +0800	pull: Fast-forward
95ddc525744e584f2dd22147b69cfd10dad3fb45 a2e5c6953adfe4ad03bf02edcc4af2d79b85299b yangliuqing <<EMAIL>> 1750314301 +0800	commit: fix action error and addRoom error
a2e5c6953adfe4ad03bf02edcc4af2d79b85299b c7e6bb009cfeb367e918a63314c8d2b4620de864 yangliuqing <<EMAIL>> 1750403152 +0800	pull: Fast-forward
c7e6bb009cfeb367e918a63314c8d2b4620de864 ae54f065deed4916fbcfb490671ac8d51032201c yangliuqing <<EMAIL>> 1750403168 +0800	commit: fix 聚合设备错误
ae54f065deed4916fbcfb490671ac8d51032201c a47e30ef9263e6846ea58af653c2322968b8682e yangliuqing <<EMAIL>> 1750403190 +0800	commit (amend): fix 设备卡片编辑错误
a47e30ef9263e6846ea58af653c2322968b8682e 0fa55ebf0874d341da483b7a736a044ae9b7987d yangliuqing <<EMAIL>> 1750403404 +0800	commit (amend): fix 设备卡片编辑错误
0fa55ebf0874d341da483b7a736a044ae9b7987d 066641b52582ee2197b05c7f3f1df86686e11d57 yangliuqing <<EMAIL>> 1751009700 +0800	pull: Fast-forward
066641b52582ee2197b05c7f3f1df86686e11d57 6394200bebb8ec5a2e19b436ebff980dda5a9ddf yangliuqing <<EMAIL>> 1751009717 +0800	commit: 联调bug：聚合设备参数错误
6394200bebb8ec5a2e19b436ebff980dda5a9ddf 95281ad3f843ab157f20f5f3e1d3e784e9365922 yangliuqing <<EMAIL>> 1751339654 +0800	pull: Fast-forward
95281ad3f843ab157f20f5f3e1d3e784e9365922 928197ed3787faa95111963ba1b60a557bf4786d yangliuqing <<EMAIL>> 1751339675 +0800	commit: 抽取公共代码
