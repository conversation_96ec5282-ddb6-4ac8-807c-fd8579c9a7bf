0000000000000000000000000000000000000000 7a162619dc9fb669b723e248aa941c69b348162f yangliuqing <<EMAIL>> 1750125810 +0800	branch: Created from refs/remotes/origin/task_SYN-T298906
7a162619dc9fb669b723e248aa941c69b348162f 5448d3e6892dbf125c69fb1c150c6fcd1fbefeb7 yangliuqing <<EMAIL>> 1750125858 +0800	commit: 联调bug修复: Invalid type: null, expected struct MemberInfo
5448d3e6892dbf125c69fb1c150c6fcd1fbefeb7 2da7eec1e18919b5552118101a128c84e99a7cbc yangliuqing <<EMAIL>> 1750138940 +0800	pull: Fast-forward
2da7eec1e18919b5552118101a128c84e99a7cbc 1bfc58d1013907f0834cd1ebac16c28f85845410 yangliuqing <<EMAIL>> 1750138954 +0800	commit: fix memberinfo error
1bfc58d1013907f0834cd1ebac16c28f85845410 4dea356a94348bc0d916c1c0d573c99cb4613f48 yangliuqing <<EMAIL>> 1750140679 +0800	commit (amend): fix memberinfo error
4dea356a94348bc0d916c1c0d573c99cb4613f48 03d739ddd728de755178ccf3ac2015227e18d093 yangliuqing <<EMAIL>> 1750141072 +0800	commit (amend): fix memberinfo error
03d739ddd728de755178ccf3ac2015227e18d093 4def2f3dc9a6a96dd05b854200421a6b9d55ad8d yangliuqing <<EMAIL>> 1750142151 +0800	commit (amend): fix memberinfo error
4def2f3dc9a6a96dd05b854200421a6b9d55ad8d c7756b76592bb1e7354892b7f0e0b6c9b06e2cac yangliuqing <<EMAIL>> 1750237962 +0800	pull: Fast-forward
c7756b76592bb1e7354892b7f0e0b6c9b06e2cac b001320a4079d4097c90b4d821cad31d1ba670a8 yangliuqing <<EMAIL>> 1750237974 +0800	commit: fix args error
b001320a4079d4097c90b4d821cad31d1ba670a8 95ddc525744e584f2dd22147b69cfd10dad3fb45 yangliuqing <<EMAIL>> 1750314287 +0800	pull: Fast-forward
95ddc525744e584f2dd22147b69cfd10dad3fb45 a2e5c6953adfe4ad03bf02edcc4af2d79b85299b yangliuqing <<EMAIL>> 1750314301 +0800	commit: fix action error and addRoom error
a2e5c6953adfe4ad03bf02edcc4af2d79b85299b c7e6bb009cfeb367e918a63314c8d2b4620de864 yangliuqing <<EMAIL>> 1750403152 +0800	pull: Fast-forward
c7e6bb009cfeb367e918a63314c8d2b4620de864 ae54f065deed4916fbcfb490671ac8d51032201c yangliuqing <<EMAIL>> 1750403168 +0800	commit: fix 聚合设备错误
ae54f065deed4916fbcfb490671ac8d51032201c a47e30ef9263e6846ea58af653c2322968b8682e yangliuqing <<EMAIL>> 1750403190 +0800	commit (amend): fix 设备卡片编辑错误
a47e30ef9263e6846ea58af653c2322968b8682e 0fa55ebf0874d341da483b7a736a044ae9b7987d yangliuqing <<EMAIL>> 1750403404 +0800	commit (amend): fix 设备卡片编辑错误
0fa55ebf0874d341da483b7a736a044ae9b7987d 066641b52582ee2197b05c7f3f1df86686e11d57 yangliuqing <<EMAIL>> 1751009700 +0800	pull: Fast-forward
066641b52582ee2197b05c7f3f1df86686e11d57 6394200bebb8ec5a2e19b436ebff980dda5a9ddf yangliuqing <<EMAIL>> 1751009717 +0800	commit: 联调bug：聚合设备参数错误
6394200bebb8ec5a2e19b436ebff980dda5a9ddf 95281ad3f843ab157f20f5f3e1d3e784e9365922 yangliuqing <<EMAIL>> 1751339654 +0800	pull: Fast-forward
95281ad3f843ab157f20f5f3e1d3e784e9365922 928197ed3787faa95111963ba1b60a557bf4786d yangliuqing <<EMAIL>> 1751339675 +0800	commit: 抽取公共代码
