
<manifest> 
  <remote name="origin" fetch="ssh://*****************:18022/"/>  
  <default remote="origin" revision="master" sync-j="4"/>  
  <project name="uplus/rust/resource_rust" path="resource_rust" revision="refs/tags/0.2.0.2025022601" rust="rust_resource"/>
  <project name="uplus/rust/storage" path="storage_rust" revision="refs/tags/0.3.0.2025022601" rust="rust_storage"/>
  <project name="uplus/rust/logic_engine_rust" path="logic_engine_rust" revision="refs/tags/0.3.0.2025022601" rust="rust_logicEngine"/>
  <project name="uplus/rust/uplus_main_rust" path="uplus_main_rust" revision="refs/tags/1.0.0.2025030401" rust="rust_main_uplus"/>
  <project name="uplus/rust/usdk_rust" path="usdk_rust" revision="refs/tags/1.0.0.2025022601" rust="rust_usdk"/>
  <project name="uplus/rust/request_rust" path="request_rust" revision="refs/tags/0.1.0.2025022602" rust="request_rust"/>
  <project name="uplus/rust/updevice_rust" path="updevice_rust" revision="refs/tags/0.4.2.2025022601" rust="rust_updevice"/>
  <project name="uplus/rust/task_manager_rust" path="task_manager_rust" revision="refs/tags/0.1.0.2025022602" rust="task_manager_rust"/>
  <project name="uplus/rust/security_rust" path="security_rust" revision="refs/tags/0.2.0.2025022601" rust="rust_security"/>
  <project name="uplus/rust/userdomain_rust" path="userdomain_rust" revision="refs/tags/0.3.0.2025022602" rust="rust_userdomain"/>
</manifest>
