{"meta": {"stableOrder": true}, "lockfileVersion": 3, "ATTENTION": "THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.", "specifiers": {"@ohos/hamock@1.0.0": "@ohos/hamock@1.0.0", "@ohos/hypium@1.0.18": "@ohos/hypium@1.0.18"}, "packages": {"@ohos/hamock@1.0.0": {"name": "@ohos/hamock", "version": "1.0.0", "integrity": "sha512-K6lDPYc6VkKe6ZBNQa9aoG+ZZMiwqfcR/7yAVFSUGIuOAhPvCJAo9+t1fZnpe0dBRBPxj2bxPPbKh69VuyAtDg==", "resolved": "https://repo.harmonyos.com/ohpm/@ohos/hamock/-/hamock-1.0.0.har", "registryType": "ohpm"}, "@ohos/hypium@1.0.18": {"name": "@ohos/hypium", "version": "1.0.18", "integrity": "sha512-RGe/iLGdeywdQilMWZsHKUoiE9OJ+9QxQsorF92R2ImLNVHVhbpSePNITGpW7TnvLgOIP/jscOqfIOhk6X7XRQ==", "resolved": "https://repo.harmonyos.com/ohpm/@ohos/hypium/-/hypium-1.0.18.har", "registryType": "ohpm"}}}