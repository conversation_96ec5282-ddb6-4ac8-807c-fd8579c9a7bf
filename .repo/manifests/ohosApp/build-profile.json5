{
  "app": {
    "signingConfigs": [
      {
        "name": "default",
        "type": "HarmonyOS",
        "material": {
          "certpath": "/Users/<USER>/.ohos/config/default_ohosApp_qsG-9AyK8NJWLorRlc-iSdNrR7QzZvtXZoKigZQgVbE=.cer",
          "storePassword": "0000001A1313C27A1FB7165D160839149BF7F89B265DFEBDC06B09CAB849E1A46A71580529E897237E7E",
          "keyAlias": "debugKey",
          "keyPassword": "0000001AF762B3CCDF2D531A642B360F2EB652429D15A27F972A892EDD9BE79E0C7A77AE9194EB72595C",
          "profile": "/Users/<USER>/.ohos/config/default_ohosApp_qsG-9AyK8NJWLorRlc-iSdNrR7QzZvtXZoKigZQgVbE=.p7b",
          "signAlg": "SHA256withECDSA",
          "storeFile": "/Users/<USER>/.ohos/config/default_ohosApp_qsG-9AyK8NJWLorRlc-iSdNrR7QzZvtXZoKigZQgVbE=.p12"
        }
      }
    ],
    "products": [
      {
        "name": "default",
        "signingConfig": "default",
        "compatibleSdkVersion": "5.0.0(12)",
        "runtimeOS": "HarmonyOS",
      }
    ],
    "buildModeSet": [
      {
        "name": "debug",
      },
      {
        "name": "release"
      }
    ]
  },
  "modules": [
    {
      "name": "entry",
      "srcPath": "./entry",
      "targets": [
        {
          "name": "default",
          "applyToProducts": [
            "default"
          ]
        }
      ]
    },
    {
      "name": "rust_uplus",
      "srcPath": "./rust_uplus",
    }
  ]
}