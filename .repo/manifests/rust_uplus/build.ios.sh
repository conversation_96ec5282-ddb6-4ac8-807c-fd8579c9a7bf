#!/bin/bash

# Just for local debugging

export IPHONEOS_DEPLOYMENT_TARGET=12.0
export RUSTFLAGS="-C link-arg=-miphoneos-version-min=12.0"

# BUILD_MODE=release
BUILD_MODE=debug

INCLUEDE_SIMULATOR=false
# INCLUEDE_SIMULATOR=true

STRIP=false
# STRIP=true

PROJECT_NAME=rust_uplus
POD_NAME=RustUplus

rm -rf ../iosApp/${PROJECT_NAME}/generated
rm -rf ../iosApp/build
rm -rf ../iosApp/publish
mkdir -p ../iosApp/${PROJECT_NAME}/generated/include
mkdir -p ../iosApp/publish

cargo clean

PROFILE="release"
if [ "$BUILD_MODE" = "debug" ]; then
  PROFILE="dev"
fi

cargo build -p ${PROJECT_NAME} --target aarch64-apple-ios --profile $PROFILE --lib --features ios || exit 1
if [ "$STRIP" = "true" ]; then
  xcrun strip -xS ../../target/aarch64-apple-ios/$BUILD_MODE/lib${PROJECT_NAME}.a -o ../../target/aarch64-apple-ios/$BUILD_MODE/lib${PROJECT_NAME}.a || exit 1
fi

if [ -f "cbindgen.toml" ]; then
    cbindgen -v --output ../iosApp/${PROJECT_NAME}/generated/include/${PROJECT_NAME}.h --config cbindgen.toml -- src/features/ios.rs || exit 1
else
    cbindgen -v --output ../iosApp/${PROJECT_NAME}/generated/include/${PROJECT_NAME}.h --lang c -- src/features/ios.rs  || exit 1
fi

if [ "$INCLUEDE_SIMULATOR" = "true" ]; then
  cargo build -p ${PROJECT_NAME} --target aarch64-apple-ios-sim --profile $PROFILE --lib --features ios || exit 1
  cargo build -p ${PROJECT_NAME} --target x86_64-apple-ios --profile $PROFILE --lib --features ios || exit 1
  if [ "$STRIP" = "true" ]; then
    xcrun strip -xS \
      ../../target/aarch64-apple-ios-sim/$BUILD_MODE/lib${PROJECT_NAME}.a \
      -o ../../target/aarch64-apple-ios-sim/$BUILD_MODE/lib${PROJECT_NAME}.a \
      || exit 1

    xcrun strip -xS \
      ../../target/x86_64-apple-ios/$BUILD_MODE/lib${PROJECT_NAME}.a \
      -o ../../target/x86_64-apple-ios/$BUILD_MODE/lib${PROJECT_NAME}.a \
      || exit 1
  fi
  lipo -create \
    ../../target/aarch64-apple-ios/$BUILD_MODE/lib${PROJECT_NAME}.a \
    ../../target/aarch64-apple-ios-sim/$BUILD_MODE/lib${PROJECT_NAME}.a \
    ../../target/x86_64-apple-ios/$BUILD_MODE/lib${PROJECT_NAME}.a \
    -output \
    ../iosApp/${PROJECT_NAME}/generated/lib${PROJECT_NAME}.a
else
  cp ../../target/aarch64-apple-ios/$BUILD_MODE/lib${PROJECT_NAME}.a ../iosApp/${PROJECT_NAME}/generated || exit 1
fi

cd ../iosApp
pod deintegrate
rm -rf Podfile.lock
pod install

CONFIGURATION=Release
if [ "$BUILD_MODE" = "debug" ]; then
  CONFIGURATION=Debug
fi

xcodebuild \
  -workspace iosApp.xcworkspace \
  -scheme $POD_NAME \
  -configuration $CONFIGURATION \
  -sdk iphoneos \
  -derivedDataPath build \
  -quiet \
  || exit 1

# find build \( -name "${POD_NAME}.framework" -o -name "${POD_NAME}.framework.dSYM" \) -exec cp -R {} publish \;
cp -r build/Build/Products/${CONFIGURATION}-iphoneos/${POD_NAME}.framework publish
if [ "$CONFIGURATION" = "Release" ]; then
  cp -r build/Build/Products/${CONFIGURATION}-iphoneos/${POD_NAME}.framework.dSYM publish
fi

if [ "$INCLUEDE_SIMULATOR" = "true" ]; then
  xcodebuild \
    -workspace iosApp.xcworkspace \
    -scheme $POD_NAME \
    -configuration $CONFIGURATION \
    -sdk iphonesimulator \
    -derivedDataPath build \
    -quiet \
    || exit 1
  lipo -create \
    publish/${POD_NAME}.framework/${POD_NAME} \
    build/Build/Products/${CONFIGURATION}-iphonesimulator/${POD_NAME}.framework/${POD_NAME} \
    -output publish/${POD_NAME}.framework/${POD_NAME}
fi

rm -f publish/${POD_NAME}.framework/*.tbd
cp ${POD_NAME}/${POD_NAME}.podspec publish
lipo -info publish/${POD_NAME}.framework/${POD_NAME}
