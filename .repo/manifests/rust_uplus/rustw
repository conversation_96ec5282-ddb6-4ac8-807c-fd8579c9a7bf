#!/bin/sh

homeDir=$(pwd)
cd ../../
TOML_FILE="Cargo.toml"
members=$(awk '/members = \[/,/\]/' "$TOML_FILE" | grep -v '\[' | grep -v '\]' | tr -d ' ",')
members_array=$(echo "$members" | tr '\n' ' ')

show_help() {
    echo "使用方法: \$0 [参数]"
    echo
    echo "参数:"
    echo "  frb       flutter_rust_bridge_codegen 生成代码"
    echo "  clean     清理工作区"
    echo "  help      显示此帮助信息"
}

start_frb_gen() {
  flutter_rust_bridge_codegen generate --no-web --stop-on-error
}

start_clean() {
  echo "正在清理工作区..."
  cargo clean
  for member in $members_array; do
      case "$member" in
          *)
            echo "remove $member/target"
            rm -rf "$member"/target
          ;;
      esac
  done
}

case "$1" in
    frb)
        cd "$homeDir/.." || { echo "Need change to frb-yaml directory"; exit 1; }
        start_frb_gen
        ;;
    clean)
        start_clean
        ;;
    help)
        show_help
        ;;
    *)
        echo "无效的参数: $1"
        exit 1
        ;;
esac