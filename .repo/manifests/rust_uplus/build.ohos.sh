#!/bin/bash

PWD=$(pwd)
ARCHS=("ohos-arm64-v8a")

for arch in "${ARCHS[@]}"; do
  case $arch in
    "ohos-arm64-v8a")
      TARGET_HOST=aarch64-unknown-linux-ohos
      ARCH_DIR="arm64-v8a"
      ;;
    "ohos-armeabi-v7a")
      TARGET_HOST=armv7-unknown-linux-ohos
      ARCH_DIR="armeabi-v7a"
      ;;
  esac

  # 配置为自己本地的路径
  export CC="$OH_NDK_TOOLCHAIN/bin/$TARGET_HOST-clang"
  export OPENSSL_DIR=$HOME/lib/rust/env/$arch/openssl
  export OPENSSL_LIB_DIR=$OPENSSL_DIR/lib
  export OPENSSL_INCLUDE_DIR=$OPENSSL_DIR/include
  cargo build --target "$TARGET_HOST" --features ohos --target-dir target
  cp -a target/"$TARGET_HOST"/debug/librust_uplus.so ../ohosApp/rust_uplus/libs/"$ARCH_DIR"

done
