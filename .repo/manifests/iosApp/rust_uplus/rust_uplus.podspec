Pod::Spec.new do |spec|
  spec.name         = "rust_uplus"
  spec.version      = "0.1.0"
  spec.summary      = "rust_uplus static library for iOS."
  spec.description  = <<-DESC
  rust_uplus static library for iOS.
                   DESC

  spec.homepage     = "https://git.haier.net/uplus/rust/uplus_rust"
  spec.license      = "MIT"
  spec.author       = { "lubiao" => "<EMAIL>" }
  spec.platform     = :ios, "12.0"
  spec.swift_version = "5.0"
  spec.source       = { :git => "https://git.haier.net/uplus/shell/cocoapods/rust_uplus.git", :tag => "#{spec.version}" }

  spec.libraries = "sqlite3"
  spec.vendored_libraries = "**/*.a"

  spec.pod_target_xcconfig = {
    'DEFINES_MODULE' => 'YES'
  }
end
