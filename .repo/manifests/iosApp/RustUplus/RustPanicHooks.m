//
//  RustPanicHooks.m
//  RustUplus
//
//  Created by 路标 on 2024/11/20.
//

#import "RustPanicHooks.h"
#import <pthread/pthread.h>
#import "rust_uplus.h"

NSString * const RustPanicNotification = @"RustPanicNotification";
NSString * const RustPanicUserInfoNameKey = @"name";
NSString * const RustPanicUserInfoReasonKey = @"reason";
NSString * const RustPanicUserInfoStackTraceKey = @"stackTrace";

void onRustPanic(const char *name,
                 const char *reason,
                 struct RustStackTrace *stacktrace) {
    NSString *panicName = [[NSString alloc] initWithCString:name encoding:NSUTF8StringEncoding];
    NSString *panickReason = [[NSString alloc] initWithCString:reason encoding:NSUTF8StringEncoding];
    free((void *)name);
    free((void *)reason);

    NSMutableArray<NSString *> *backtrace = [NSMutableArray array];
    struct RustStackTrace *stack = stacktrace;
    while (stack != NULL) {
        if (stack -> frame) {
            NSString *frameInfo = [[NSString alloc] initWithCString:(stack -> frame) encoding:NSUTF8StringEncoding];
            [backtrace addObject:frameInfo];
            free((void *)(stack -> frame));
        }
        stack = stack -> next;
    }
    NSDictionary *userInfo = @{
        RustPanicUserInfoNameKey: panicName,
        RustPanicUserInfoReasonKey: panickReason,
        RustPanicUserInfoStackTraceKey: backtrace,
    };

    [[NSNotificationCenter defaultCenter] postNotificationName:RustPanicNotification object:nil userInfo:userInfo];
}

@implementation RustPanicHooks

+ (void)load {
    hook_rust_panic(onRustPanic);
}

@end
