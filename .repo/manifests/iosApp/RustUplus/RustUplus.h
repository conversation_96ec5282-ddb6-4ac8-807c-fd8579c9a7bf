//
//  RustUplus.h
//  RustUplus
//
//  Created by 路标 on 2024/10/28.
//

#import <Foundation/Foundation.h>

//! Project version number for RustUplus.
FOUNDATION_EXPORT double RustUplusVersionNumber;

//! Project version string for RustUplus.
FOUNDATION_EXPORT const unsigned char RustUplusVersionString[];

// In this header, you should import all the public headers of your framework using statements like #import <RustUplus/PublicHeader.h>

extern NSString * const RustPanicNotification;
extern NSString * const RustPanicUserInfoNameKey;
extern NSString * const RustPanicUserInfoReasonKey;
extern NSString * const RustPanicUserInfoStackTraceKey;

#import <RustUplus/rust_uplus.h>
