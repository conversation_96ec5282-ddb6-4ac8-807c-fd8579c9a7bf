platform :ios, '12.0'

target 'iosApp' do
end

target 'RustUplus' do
  pod 'rust_uplus', :path => './rust_uplus'
end

post_install do |installer|
  installer.aggregate_targets.each do |aggregate_target|
    user_project = Xcodeproj::Project.open(aggregate_target.user_project_path)

    user_project.native_targets.each do |native_target|
      if native_target.name == "RustUplus"
        native_target.build_configurations.each do |config|
          config.build_settings['OTHER_LDFLAGS'] = '$(inherited) -force_load "${SRCROOT}/rust_uplus/generated/librust_uplus.a"'
        end
      end
    end
    user_project.save
  end
end
