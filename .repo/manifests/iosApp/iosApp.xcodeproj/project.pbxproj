// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXBuildFile section */
		08F3B297A626AF5441B96DF9 /* libPods-RustUplus.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 9F991BF0490A9323C98452EC /* libPods-RustUplus.a */; };
		9503549F2CD0C80B00F3A368 /* rust_uplus.h in Headers */ = {isa = PBXBuildFile; fileRef = 9503549E2CD0C80B00F3A368 /* rust_uplus.h */; settings = {ATTRIBUTES = (Public, ); }; };
		954BF40C2CEDD2A7002DB637 /* RustPanicHooks.h in Headers */ = {isa = PBXBuildFile; fileRef = 954BF40A2CEDD2A7002DB637 /* RustPanicHooks.h */; };
		954BF40D2CEDD2A7002DB637 /* RustPanicHooks.m in Sources */ = {isa = PBXBuildFile; fileRef = 954BF40B2CEDD2A7002DB637 /* RustPanicHooks.m */; };
		95CBF5BF2CCF813A00EEA459 /* AppDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = 95CBF5BE2CCF813A00EEA459 /* AppDelegate.m */; };
		95CBF5CA2CCF813D00EEA459 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 95CBF5C92CCF813D00EEA459 /* Assets.xcassets */; };
		95CBF5CD2CCF813D00EEA459 /* Base in Resources */ = {isa = PBXBuildFile; fileRef = 95CBF5CC2CCF813D00EEA459 /* Base */; };
		95CBF5D02CCF813D00EEA459 /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 95CBF5CF2CCF813D00EEA459 /* main.m */; };
		95CBF5D82CCF82AC00EEA459 /* RootViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95CBF5D72CCF82AC00EEA459 /* RootViewController.swift */; };
		95CBF5E12CCF852F00EEA459 /* RustUplus.h in Headers */ = {isa = PBXBuildFile; fileRef = 95CBF5E02CCF852F00EEA459 /* RustUplus.h */; settings = {ATTRIBUTES = (Public, ); }; };
		95CBF5E42CCF852F00EEA459 /* RustUplus.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 95CBF5DE2CCF852F00EEA459 /* RustUplus.framework */; };
		95CBF5EF2CCF8C2D00EEA459 /* RustUplus.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = 95CBF5DE2CCF852F00EEA459 /* RustUplus.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		AF4FBE9A2D3CA9666BA8EC1B /* libPods-iosApp.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 0E63245E1915F4D6741EA240 /* libPods-iosApp.a */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		95CBF5E22CCF852F00EEA459 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 95CBF5B22CCF813A00EEA459 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 95CBF5DD2CCF852F00EEA459;
			remoteInfo = RustUplus;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		95CBF5E62CCF852F00EEA459 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				95CBF5EF2CCF8C2D00EEA459 /* RustUplus.framework in Embed Frameworks */,
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		0E63245E1915F4D6741EA240 /* libPods-iosApp.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-iosApp.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		3DC1B197E4B431A65BCD1790 /* Pods-RustUplus.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-RustUplus.debug.xcconfig"; path = "Target Support Files/Pods-RustUplus/Pods-RustUplus.debug.xcconfig"; sourceTree = "<group>"; };
		9503549E2CD0C80B00F3A368 /* rust_uplus.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = rust_uplus.h; path = rust_uplus/generated/include/rust_uplus.h; sourceTree = SOURCE_ROOT; };
		954BF40A2CEDD2A7002DB637 /* RustPanicHooks.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RustPanicHooks.h; sourceTree = "<group>"; };
		954BF40B2CEDD2A7002DB637 /* RustPanicHooks.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RustPanicHooks.m; sourceTree = "<group>"; };
		9584511F2CD096DD0096D7A6 /* libsqlite3.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libsqlite3.tbd; path = usr/lib/libsqlite3.tbd; sourceTree = SDKROOT; };
		95CBF5BA2CCF813A00EEA459 /* iosApp.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = iosApp.app; sourceTree = BUILT_PRODUCTS_DIR; };
		95CBF5BD2CCF813A00EEA459 /* AppDelegate.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AppDelegate.h; sourceTree = "<group>"; };
		95CBF5BE2CCF813A00EEA459 /* AppDelegate.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AppDelegate.m; sourceTree = "<group>"; };
		95CBF5C92CCF813D00EEA459 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		95CBF5CC2CCF813D00EEA459 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/LaunchScreen.storyboard; sourceTree = "<group>"; };
		95CBF5CE2CCF813D00EEA459 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		95CBF5CF2CCF813D00EEA459 /* main.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = main.m; sourceTree = "<group>"; };
		95CBF5D62CCF82AB00EEA459 /* iosApp-Bridging-Header.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "iosApp-Bridging-Header.h"; sourceTree = "<group>"; };
		95CBF5D72CCF82AC00EEA459 /* RootViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RootViewController.swift; sourceTree = "<group>"; };
		95CBF5DE2CCF852F00EEA459 /* RustUplus.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = RustUplus.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		95CBF5E02CCF852F00EEA459 /* RustUplus.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RustUplus.h; sourceTree = "<group>"; };
		984E884F70CE76441787699E /* Pods-iosApp.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-iosApp.debug.xcconfig"; path = "Target Support Files/Pods-iosApp/Pods-iosApp.debug.xcconfig"; sourceTree = "<group>"; };
		9F991BF0490A9323C98452EC /* libPods-RustUplus.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-RustUplus.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		B8A21647AEEAFDC56D3FEEED /* Pods-iosApp.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-iosApp.release.xcconfig"; path = "Target Support Files/Pods-iosApp/Pods-iosApp.release.xcconfig"; sourceTree = "<group>"; };
		F8E39F41F0CC2BAC8017AD7B /* Pods-RustUplus.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-RustUplus.release.xcconfig"; path = "Target Support Files/Pods-RustUplus/Pods-RustUplus.release.xcconfig"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		95CBF5B72CCF813A00EEA459 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				95CBF5E42CCF852F00EEA459 /* RustUplus.framework in Frameworks */,
				AF4FBE9A2D3CA9666BA8EC1B /* libPods-iosApp.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		95CBF5DB2CCF852F00EEA459 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				08F3B297A626AF5441B96DF9 /* libPods-RustUplus.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		25FB104AD7A5FE4CB26DA872 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				9584511F2CD096DD0096D7A6 /* libsqlite3.tbd */,
				9F991BF0490A9323C98452EC /* libPods-RustUplus.a */,
				0E63245E1915F4D6741EA240 /* libPods-iosApp.a */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		95CBF5B12CCF813A00EEA459 = {
			isa = PBXGroup;
			children = (
				95CBF5BC2CCF813A00EEA459 /* iosApp */,
				95CBF5DF2CCF852F00EEA459 /* RustUplus */,
				95CBF5BB2CCF813A00EEA459 /* Products */,
				CDEBF301BF87A40E1709423D /* Pods */,
				25FB104AD7A5FE4CB26DA872 /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		95CBF5BB2CCF813A00EEA459 /* Products */ = {
			isa = PBXGroup;
			children = (
				95CBF5BA2CCF813A00EEA459 /* iosApp.app */,
				95CBF5DE2CCF852F00EEA459 /* RustUplus.framework */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		95CBF5BC2CCF813A00EEA459 /* iosApp */ = {
			isa = PBXGroup;
			children = (
				95CBF5BD2CCF813A00EEA459 /* AppDelegate.h */,
				95CBF5BE2CCF813A00EEA459 /* AppDelegate.m */,
				95CBF5C92CCF813D00EEA459 /* Assets.xcassets */,
				95CBF5CB2CCF813D00EEA459 /* LaunchScreen.storyboard */,
				95CBF5CE2CCF813D00EEA459 /* Info.plist */,
				95CBF5CF2CCF813D00EEA459 /* main.m */,
				95CBF5D72CCF82AC00EEA459 /* RootViewController.swift */,
				95CBF5D62CCF82AB00EEA459 /* iosApp-Bridging-Header.h */,
			);
			path = iosApp;
			sourceTree = "<group>";
		};
		95CBF5DF2CCF852F00EEA459 /* RustUplus */ = {
			isa = PBXGroup;
			children = (
				9503549E2CD0C80B00F3A368 /* rust_uplus.h */,
				95CBF5E02CCF852F00EEA459 /* RustUplus.h */,
				954BF40A2CEDD2A7002DB637 /* RustPanicHooks.h */,
				954BF40B2CEDD2A7002DB637 /* RustPanicHooks.m */,
			);
			path = RustUplus;
			sourceTree = "<group>";
		};
		CDEBF301BF87A40E1709423D /* Pods */ = {
			isa = PBXGroup;
			children = (
				3DC1B197E4B431A65BCD1790 /* Pods-RustUplus.debug.xcconfig */,
				F8E39F41F0CC2BAC8017AD7B /* Pods-RustUplus.release.xcconfig */,
				984E884F70CE76441787699E /* Pods-iosApp.debug.xcconfig */,
				B8A21647AEEAFDC56D3FEEED /* Pods-iosApp.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		95CBF5D92CCF852F00EEA459 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				9503549F2CD0C80B00F3A368 /* rust_uplus.h in Headers */,
				954BF40C2CEDD2A7002DB637 /* RustPanicHooks.h in Headers */,
				95CBF5E12CCF852F00EEA459 /* RustUplus.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		95CBF5B92CCF813A00EEA459 /* iosApp */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 95CBF5D32CCF813D00EEA459 /* Build configuration list for PBXNativeTarget "iosApp" */;
			buildPhases = (
				F75BB13C839BF265BE8C1465 /* [CP] Check Pods Manifest.lock */,
				95CBF5B62CCF813A00EEA459 /* Sources */,
				95CBF5B72CCF813A00EEA459 /* Frameworks */,
				95CBF5B82CCF813A00EEA459 /* Resources */,
				95CBF5E62CCF852F00EEA459 /* Embed Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
				95CBF5E32CCF852F00EEA459 /* PBXTargetDependency */,
			);
			name = iosApp;
			productName = iosApp;
			productReference = 95CBF5BA2CCF813A00EEA459 /* iosApp.app */;
			productType = "com.apple.product-type.application";
		};
		95CBF5DD2CCF852F00EEA459 /* RustUplus */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 95CBF5E92CCF852F00EEA459 /* Build configuration list for PBXNativeTarget "RustUplus" */;
			buildPhases = (
				A597024F7F73A0F60479B95C /* [CP] Check Pods Manifest.lock */,
				95CBF5D92CCF852F00EEA459 /* Headers */,
				95CBF5DA2CCF852F00EEA459 /* Sources */,
				95CBF5DB2CCF852F00EEA459 /* Frameworks */,
				95CBF5DC2CCF852F00EEA459 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = RustUplus;
			productName = RustUplus;
			productReference = 95CBF5DE2CCF852F00EEA459 /* RustUplus.framework */;
			productType = "com.apple.product-type.framework";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		95CBF5B22CCF813A00EEA459 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastUpgradeCheck = 1540;
				TargetAttributes = {
					95CBF5B92CCF813A00EEA459 = {
						CreatedOnToolsVersion = 15.4;
						LastSwiftMigration = 1540;
					};
					95CBF5DD2CCF852F00EEA459 = {
						CreatedOnToolsVersion = 15.4;
					};
				};
			};
			buildConfigurationList = 95CBF5B52CCF813A00EEA459 /* Build configuration list for PBXProject "iosApp" */;
			compatibilityVersion = "Xcode 12.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 95CBF5B12CCF813A00EEA459;
			productRefGroup = 95CBF5BB2CCF813A00EEA459 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				95CBF5B92CCF813A00EEA459 /* iosApp */,
				95CBF5DD2CCF852F00EEA459 /* RustUplus */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		95CBF5B82CCF813A00EEA459 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				95CBF5CA2CCF813D00EEA459 /* Assets.xcassets in Resources */,
				95CBF5CD2CCF813D00EEA459 /* Base in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		95CBF5DC2CCF852F00EEA459 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		A597024F7F73A0F60479B95C /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-RustUplus-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		F75BB13C839BF265BE8C1465 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-iosApp-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		95CBF5B62CCF813A00EEA459 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				95CBF5BF2CCF813A00EEA459 /* AppDelegate.m in Sources */,
				95CBF5D02CCF813D00EEA459 /* main.m in Sources */,
				95CBF5D82CCF82AC00EEA459 /* RootViewController.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		95CBF5DA2CCF852F00EEA459 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				954BF40D2CEDD2A7002DB637 /* RustPanicHooks.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		95CBF5E32CCF852F00EEA459 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 95CBF5DD2CCF852F00EEA459 /* RustUplus */;
			targetProxy = 95CBF5E22CCF852F00EEA459 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin PBXVariantGroup section */
		95CBF5CB2CCF813D00EEA459 /* LaunchScreen.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				95CBF5CC2CCF813D00EEA459 /* Base */,
			);
			name = LaunchScreen.storyboard;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		95CBF5D12CCF813D00EEA459 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.5;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
			};
			name = Debug;
		};
		95CBF5D22CCF813D00EEA459 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.5;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		95CBF5D42CCF813D00EEA459 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 984E884F70CE76441787699E /* Pods-iosApp.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CLANG_ENABLE_MODULES = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = PP27UD8NYZ;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = iosApp/Info.plist;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIMainStoryboardFile = "";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = "$(inherited)";
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.haier.uhome.Uplus;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = Uplus99Dev;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OBJC_BRIDGING_HEADER = "iosApp/iosApp-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		95CBF5D52CCF813D00EEA459 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = B8A21647AEEAFDC56D3FEEED /* Pods-iosApp.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CLANG_ENABLE_MODULES = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Distribution";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = PP27UD8NYZ;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = iosApp/Info.plist;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIMainStoryboardFile = "";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = "$(inherited)";
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.haier.uhome.Uplus;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = "XC Ad Hoc: com.haier.uhome.Uplus";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OBJC_BRIDGING_HEADER = "iosApp/iosApp-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		95CBF5E72CCF852F00EEA459 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 3DC1B197E4B431A65BCD1790 /* Pods-RustUplus.debug.xcconfig */;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/rust_uplus",
				);
				MARKETING_VERSION = 1.0;
				MODULE_VERIFIER_SUPPORTED_LANGUAGES = "objective-c objective-c++";
				MODULE_VERIFIER_SUPPORTED_LANGUAGE_STANDARDS = "gnu17 gnu++20";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-force_load",
					"\"${SRCROOT}/rust_uplus/generated/librust_uplus.a\"",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.haier.uhome.RustUplus;
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		95CBF5E82CCF852F00EEA459 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = F8E39F41F0CC2BAC8017AD7B /* Pods-RustUplus.release.xcconfig */;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/rust_uplus",
				);
				MARKETING_VERSION = 1.0;
				MODULE_VERIFIER_SUPPORTED_LANGUAGES = "objective-c objective-c++";
				MODULE_VERIFIER_SUPPORTED_LANGUAGE_STANDARDS = "gnu17 gnu++20";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-force_load",
					"\"${SRCROOT}/rust_uplus/generated/librust_uplus.a\"",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.haier.uhome.RustUplus;
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		95CBF5B52CCF813A00EEA459 /* Build configuration list for PBXProject "iosApp" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				95CBF5D12CCF813D00EEA459 /* Debug */,
				95CBF5D22CCF813D00EEA459 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		95CBF5D32CCF813D00EEA459 /* Build configuration list for PBXNativeTarget "iosApp" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				95CBF5D42CCF813D00EEA459 /* Debug */,
				95CBF5D52CCF813D00EEA459 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		95CBF5E92CCF852F00EEA459 /* Build configuration list for PBXNativeTarget "RustUplus" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				95CBF5E72CCF852F00EEA459 /* Debug */,
				95CBF5E82CCF852F00EEA459 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 95CBF5B22CCF813A00EEA459 /* Project object */;
}
