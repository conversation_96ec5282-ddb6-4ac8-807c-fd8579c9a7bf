package com.haier.uhome.uplus.rust.smarthome.app

import android.app.Application
import android.os.Build
import xcrash.XCrash
import java.io.File

/**
 * Created by liuqing.yang
 * 2024/11/22.
 */
class App : Application() {
    override fun onCreate() {
        super.onCreate()
        initXCrash()
    }

    private fun initXCrash() {
        val appLogFile = File(getExternalFilesDir(null), "logs")
        val crashSavePath = File(appLogFile, "crashreport")
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            checkAndCreateDir(crashSavePath)
        }
        val initParameters = XCrash.InitParameters()
            .setJavaRethrow(true)
            .setJavaLogCountMax(20)
            .setJavaDumpAllThreads(false)
            .setJavaCallback { _, _ -> }
            .setNativeRethrow(true)
            .setNativeLogCountMax(20)
            .setNativeCallback { _, _ -> }
            .setAnrRethrow(true)
            .setAnrLogCountMax(5)
            .setAnrCallback { _, _ -> }
            .setLogDir(crashSavePath.absolutePath)
        XCrash.init(this, initParameters)
    }

    private fun checkAndCreateDir(dir: File) {
        runCatching {
            if (!dir.exists()) {
                dir.mkdirs()
            }
        }
    }
}