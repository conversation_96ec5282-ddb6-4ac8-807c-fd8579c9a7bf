package com.haier.uhome.uplus.rust.smarthome.app

import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import com.haier.uhome.uplus.rust.smarthome.app.databinding.ActivityMainBinding

class MainActivity : AppCompatActivity() {
    companion object {
        private const val TAG = "MainActivity"

        init {
            System.loadLibrary("rust_uplus")
        }
    }

    private val binding by viewBinding<ActivityMainBinding>()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
    }
}