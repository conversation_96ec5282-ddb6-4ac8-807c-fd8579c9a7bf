[core]
	repositoryFormatVersion = 1
	filemode = true
	precomposeunicode = true
[filter "lfs"]
	smudge = git-lfs smudge --skip -- %f
	process = git-lfs filter-process --skip
[remote "origin"]
	url = ssh://*****************:18022/uplus/shell/rust/rust_uplus.git
	fetch = +refs/heads/*:refs/remotes/origin/*
[manifest]
	platform = auto
[extensions]
	preciousObjects = true
[branch "default"]
	remote = origin
	merge = refs/heads/master
[repo]
	existingprojectcount = 0
	newprojectcount = 10
[repo "syncstate.main"]
	synctime = 2025-03-05T06:59:29.664646+00:00
	version = 1
[repo "syncstate.sys"]
	argv = ['/Volumes/Samsung_X5/liuqing.yang/work/haier/rust/repo_uplus/.repo/repo/main.py', '--repo-dir=/Volumes/Samsung_X5/liuqing.yang/work/haier/rust/repo_uplus/.repo', '--wrapper-version=2.50', '--wrapper-path=/Users/<USER>/bin/repo', '--', 'sync']
[repo "syncstate.options"]
	jobs = 4
	outermanifest = true
	jobsnetwork = 4
	jobscheckout = 4
	mpupdate = true
	clonebundle = true
	retryfetches = 0
	prune = true
	repoverify = true
	quiet = false
	verbose = false
[repo "syncstate.remote.origin"]
	url = ssh://*****************:18022/uplus/shell/rust/rust_uplus.git
	fetch = +refs/heads/*:refs/remotes/origin/*
[repo "syncstate.branch.default"]
	remote = origin
	merge = refs/heads/master
[repo "syncstate.repo"]
	existingprojectcount = 0
	newprojectcount = 10
