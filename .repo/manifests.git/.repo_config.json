{"core.repositoryformatversion": ["1"], "core.filemode": ["true"], "core.precomposeunicode": ["true"], "filter.lfs.smudge": ["git-lfs smudge --skip -- %f"], "filter.lfs.process": ["git-lfs filter-process --skip"], "remote.origin.url": ["ssh://*****************:18022/uplus/shell/rust/rust_uplus.git"], "remote.origin.fetch": ["+refs/heads/*:refs/remotes/origin/*"], "manifest.platform": ["auto"], "extensions.preciousobjects": ["true"], "branch.default.remote": ["origin"], "branch.default.merge": ["refs/heads/master"], "repo.existingprojectcount": ["0"], "repo.newprojectcount": ["10"], "repo.syncstate.main.synctime": ["2025-03-05T06:59:29.664646+00:00"], "repo.syncstate.main.version": ["1"], "repo.syncstate.sys.argv": ["['/Volumes/Samsung_X5/liuqing.yang/work/haier/rust/repo_uplus/.repo/repo/main.py', '--repo-dir=/Volumes/Samsung_X5/liuqing.yang/work/haier/rust/repo_uplus/.repo', '--wrapper-version=2.50', '--wrapper-path=/Users/<USER>/bin/repo', '--', 'sync']"], "repo.syncstate.options.jobs": ["4"], "repo.syncstate.options.outermanifest": ["true"], "repo.syncstate.options.jobsnetwork": ["4"], "repo.syncstate.options.jobscheckout": ["4"], "repo.syncstate.options.mpupdate": ["true"], "repo.syncstate.options.clonebundle": ["true"], "repo.syncstate.options.retryfetches": ["0"], "repo.syncstate.options.prune": ["true"], "repo.syncstate.options.repoverify": ["true"], "repo.syncstate.options.quiet": ["false"], "repo.syncstate.options.verbose": ["false"], "repo.syncstate.remote.origin.url": ["ssh://*****************:18022/uplus/shell/rust/rust_uplus.git"], "repo.syncstate.remote.origin.fetch": ["+refs/heads/*:refs/remotes/origin/*"], "repo.syncstate.branch.default.remote": ["origin"], "repo.syncstate.branch.default.merge": ["refs/heads/master"], "repo.syncstate.repo.existingprojectcount": ["0"], "repo.syncstate.repo.newprojectcount": ["10"]}